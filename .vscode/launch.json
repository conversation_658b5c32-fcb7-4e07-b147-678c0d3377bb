{"version": "0.2.0", "configurations": [{"name": "Python: vllmdebugger", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "/workspace/lj_vllm_study/bin/python", "justMyCode": false, "env": {"PATH": "/workspace/lj_vllm_study/bin:${env:PATH}", "VIRTUAL_ENV": "/workspace/lj_vllm_study", "CUDA_VISIBLE_DEVICES": "0,1,2,3"}}]}