#!/usr/bin/env python3
"""
快速测试脚本 - 使用最保守的配置快速验证模型是否可以加载
"""

import torch
from vllm import LLM, SamplingParams

def main():
    print("=== 快速测试 - 最保守配置 ===")
    
    # 清理GPU内存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("GPU缓存已清理")
    
    model_name = "/home/<USER>/Qwen-QwQ-32B-W8A8-SmoothQuant"
    
    try:
        print("尝试加载模型...")
        llm = LLM(
            model=model_name,
            tensor_parallel_size=1,
            max_model_len=32,  # 极小的序列长度
            enable_prefix_caching=False,
            trust_remote_code=True,
            gpu_memory_utilization=0.4,  # 非常保守的内存利用率
            block_size=4,  # 小块大小
            enforce_eager=True,
            disable_custom_all_reduce=True
        )
        
        print("✅ 模型加载成功！")
        
        # 简单测试
        prompts = ["Hi"]
        sampling_params = SamplingParams(temperature=0.0, max_tokens=3)
        
        outputs = llm.generate(prompts, sampling_params)
        for output in outputs:
            print(f"输入: {output.prompt}")
            print(f"输出: {output.outputs[0].text}")
        
        print("✅ 推理测试成功！")
        
    except Exception as e:
        print(f"❌ 失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 测试通过！现在可以逐步增加配置参数。")
    else:
        print("\n💥 测试失败，需要进一步调试。")
