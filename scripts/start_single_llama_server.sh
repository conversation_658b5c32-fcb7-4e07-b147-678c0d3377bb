#!/usr/bin/env zsh
set -euo pipefail

# vLLM OpenAI 兼容服务启动脚本（单卡）
# 可通过环境变量覆盖以下默认值：
#   MODEL_PATH           模型路径（本地目录或HF模型名）
#   SERVED_MODEL_NAME    对外暴露的模型名（客户端需用此名称调用）
#   HOST                 监听地址
#   PORT                 端口
#   TP_SIZE              张量并行大小（单卡=1）
#   GPU_MEM_UTIL         GPU 显存利用率上限（0~1）
#   MAX_MODEL_LEN        最大上下文长度
#   MAX_BATCHED_TOKENS   每批最大 token 数
#   MAX_NUM_SEQS         同时处理的最大请求数

MODEL_PATH=${MODEL_PATH:-/home/<USER>/single_llama}
SERVED_MODEL_NAME=${SERVED_MODEL_NAME:-single_llama}
HOST=${HOST:-0.0.0.0}
PORT=${PORT:-8000}
TP_SIZE=${TP_SIZE:-1}
GPU_MEM_UTIL=${GPU_MEM_UTIL:-0.90}
MAX_MODEL_LEN=${MAX_MODEL_LEN:-2048}
MAX_BATCHED_TOKENS=${MAX_BATCHED_TOKENS:-2048}
MAX_NUM_SEQS=${MAX_NUM_SEQS:-32}

LOG_DIR=${LOG_DIR:-/workspace/logs}
mkdir -p "$LOG_DIR"

# 显示 GPU 信息，便于确认设备状态
if command -v nvidia-smi >/dev/null 2>&1; then
  echo "========== nvidia-smi =========="
  nvidia-smi || true
  echo "================================"
fi

echo "Starting vLLM OpenAI server..."
echo "MODEL_PATH=$MODEL_PATH"
echo "SERVED_MODEL_NAME=$SERVED_MODEL_NAME"
echo "HOST=$HOST PORT=$PORT TP_SIZE=$TP_SIZE"
echo "GPU_MEM_UTIL=$GPU_MEM_UTIL MAX_MODEL_LEN=$MAX_MODEL_LEN"
echo "MAX_BATCHED_TOKENS=$MAX_BATCHED_TOKENS MAX_NUM_SEQS=$MAX_NUM_SEQS"

# 以前台方式启动，便于直接查看日志（需要后台可在外层加 nohup 或 tmux/screen）
exec python -m vllm.entrypoints.openai.api_server \
  --model "$MODEL_PATH" \
  --tokenizer "$MODEL_PATH" \
  --served-model-name "$SERVED_MODEL_NAME" \
  --host "$HOST" \
  --port "$PORT" \
  --tensor-parallel-size "$TP_SIZE" \
  --gpu-memory-utilization "$GPU_MEM_UTIL" \
  --max-model-len "$MAX_MODEL_LEN" \
  --max-num-batched-tokens "$MAX_BATCHED_TOKENS" \
  --max-num-seqs "$MAX_NUM_SEQS" \
  --trust-remote-code \
  --enforce-eager
