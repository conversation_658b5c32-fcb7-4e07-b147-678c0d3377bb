#!/usr/bin/env python3
"""
Embedding层CUDA device-side assert错误调试工具
专门用于调试 indexSelectSmallIndex 错误
"""

import os
import torch
import logging

# 设置CUDA调试环境变量
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ['TORCH_USE_CUDA_DSA'] = '1'
os.environ['VLLM_USE_V1'] = '0'
os.environ['VLLM_USE_TRITON_FLASH_ATTN'] = '0'

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_model_and_tokenizer():
    """检查模型和tokenizer的兼容性"""
    print("=== 模型和Tokenizer兼容性检查 ===")
    
    try:
        from transformers import AutoTokenizer, AutoConfig
        
        model_path = "/home/<USER>/single_llama"
        
        # 检查配置
        print("检查模型配置...")
        config = AutoConfig.from_pretrained(model_path, trust_remote_code=True)
        print(f"词汇表大小: {config.vocab_size}")
        print(f"模型类型: {config.model_type}")
        print(f"架构: {config.architectures}")
        
        # 检查tokenizer
        print("\n检查tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        print(f"Tokenizer词汇表大小: {len(tokenizer)}")
        print(f"Tokenizer类型: {type(tokenizer)}")
        
        # 检查特殊token
        print(f"BOS token: {tokenizer.bos_token} (ID: {tokenizer.bos_token_id})")
        print(f"EOS token: {tokenizer.eos_token} (ID: {tokenizer.eos_token_id})")
        print(f"PAD token: {tokenizer.pad_token} (ID: {tokenizer.pad_token_id})")
        print(f"UNK token: {tokenizer.unk_token} (ID: {tokenizer.unk_token_id})")
        
        # 测试tokenization
        test_texts = ["Hi", "Hello", "你好", ""]
        for text in test_texts:
            try:
                tokens = tokenizer.encode(text)
                print(f"文本 '{text}' -> tokens: {tokens}")
                
                # 检查token ID是否在有效范围内
                max_token_id = max(tokens) if tokens else 0
                if max_token_id >= config.vocab_size:
                    print(f"❌ 警告: token ID {max_token_id} 超出词汇表大小 {config.vocab_size}")
                else:
                    print(f"✅ token ID范围正常 (最大: {max_token_id})")
                    
            except Exception as e:
                print(f"❌ tokenization失败 '{text}': {e}")
        
        return True, config, tokenizer
        
    except Exception as e:
        print(f"❌ 模型/tokenizer检查失败: {e}")
        return False, None, None

def test_vllm_with_tokenizer_fix():
    """使用tokenizer修复测试vLLM"""
    print("\n=== vLLM Tokenizer修复测试 ===")
    
    success, config, tokenizer = check_model_and_tokenizer()
    if not success:
        return False
    
    try:
        from vllm import LLM, SamplingParams
        
        model_path = "/home/<USER>/single_llama"
        
        # 使用更保守的配置
        llm_config = {
            "model": model_path,
            "tensor_parallel_size": 1,
            "gpu_memory_utilization": 0.8,
            "max_model_len": 32,  # 更小的序列长度
            "enable_prefix_caching": False,
            "trust_remote_code": True,
            "block_size": 16,
            "enforce_eager": True,
            "disable_custom_all_reduce": True,
            "max_num_seqs": 1,
            # 明确指定tokenizer
            "tokenizer": model_path,
            "tokenizer_mode": "auto",
        }
        
        print("配置参数:")
        for key, value in llm_config.items():
            print(f"  {key}: {value}")
        
        print("\n开始加载模型...")
        torch.cuda.empty_cache()
        
        llm = LLM(**llm_config)
        print("✅ 模型加载成功！")
        
        # 测试不同的prompt
        test_prompts = [
            "Hi",           # 简单英文
            "Hello",        # 常见英文
            "1",            # 数字
            "a",            # 单字母
        ]
        
        for prompt in test_prompts:
            try:
                print(f"\n测试prompt: '{prompt}'")
                
                # 先用tokenizer检查
                tokens = tokenizer.encode(prompt)
                print(f"  tokens: {tokens}")
                
                # 检查token范围
                if tokens and max(tokens) >= config.vocab_size:
                    print(f"  ❌ 跳过: token ID超出范围")
                    continue
                
                sampling_params = SamplingParams(
                    temperature=0.0,
                    max_tokens=1,  # 只生成1个token
                )
                
                outputs = llm.generate([prompt], sampling_params)
                print(f"  ✅ 生成成功!")
                
                for output in outputs:
                    print(f"  输入: {output.prompt}")
                    print(f"  输出: {output.outputs[0].text}")
                
                return True
                
            except Exception as e:
                print(f"  ❌ 生成失败: {e}")
                continue
        
        print("❌ 所有prompt都失败了")
        return False
        
    except Exception as e:
        print(f"❌ vLLM测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_empty_prompt():
    """测试空prompt或特殊prompt"""
    print("\n=== 空Prompt测试 ===")
    
    try:
        from vllm import LLM, SamplingParams
        
        model_path = "/home/<USER>/single_llama"
        
        llm_config = {
            "model": model_path,
            "tensor_parallel_size": 1,
            "gpu_memory_utilization": 0.8,
            "max_model_len": 16,
            "enable_prefix_caching": False,
            "trust_remote_code": True,
            "enforce_eager": True,
            "disable_custom_all_reduce": True,
            "max_num_seqs": 1,
        }
        
        torch.cuda.empty_cache()
        llm = LLM(**llm_config)
        
        # 测试特殊的prompt
        special_prompts = [
            "<s>",          # BOS token
            "</s>",         # EOS token
            "<s>Hi",        # BOS + 文本
            "Hi</s>",       # 文本 + EOS
            "<s>Hi</s>",    # 完整格式
        ]
        
        for prompt in special_prompts:
            try:
                print(f"测试特殊prompt: '{prompt}'")
                
                sampling_params = SamplingParams(
                    temperature=0.0,
                    max_tokens=1,
                )
                
                outputs = llm.generate([prompt], sampling_params)
                print(f"✅ 特殊prompt成功!")
                
                for output in outputs:
                    print(f"输入: {output.prompt}")
                    print(f"输出: {output.outputs[0].text}")
                
                return True
                
            except Exception as e:
                print(f"❌ 特殊prompt失败: {e}")
                continue
        
        return False
        
    except Exception as e:
        print(f"❌ 空prompt测试失败: {e}")
        return False

def main():
    """主函数"""
    print("Embedding层CUDA device-side assert错误调试工具")
    print("=" * 60)
    
    # 检查模型和tokenizer
    if not check_model_and_tokenizer()[0]:
        print("❌ 模型/tokenizer检查失败")
        return False
    
    # 测试vLLM with tokenizer fix
    if test_vllm_with_tokenizer_fix():
        print("\n🎉 Tokenizer修复测试成功！")
        return True
    
    # 测试特殊prompt
    if test_empty_prompt():
        print("\n🎉 特殊prompt测试成功！")
        return True
    
    print("\n❌ 所有测试都失败了")
    print("\n可能的原因:")
    print("1. 模型文件损坏或不完整")
    print("2. tokenizer与模型不匹配")
    print("3. 词汇表大小配置错误")
    print("4. 特殊token配置问题")
    
    print("\n建议:")
    print("1. 重新下载模型文件")
    print("2. 检查模型的config.json文件")
    print("3. 尝试使用官方的tokenizer")
    print("4. 使用更小的测试模型")
    
    return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
