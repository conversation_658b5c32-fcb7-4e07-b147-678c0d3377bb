# vLLM EPLB负载平衡执行机制详细分析

## 概述

本文档基于vLLM源码深入分析EPLB（Expert Parallel Load Balancer）负载平衡函数的执行时机、频率、判断参数和获取方式。

## 1. EPLB负载平衡执行时机

### 1.1 调用位置和时机

**源码位置**: `vllm/v1/worker/gpu_model_runner.py:1574`

```python
def execute_model(self, scheduler_output, intermediate_tensors):
    # ... 模型前向传播 ...
    
    # 🔥 关键: 模型前向传播完成后立即调用
    model_output = self.model(
        input_ids=input_ids,
        positions=positions,
        intermediate_tensors=intermediate_tensors,
        inputs_embeds=inputs_embeds,
    )
    
    # ... 处理输出、采样等 ...
    
    # ✅ EPLB step调用时机: 每次推理step完成后
    self.eplb_step()  # 第1574行
    
    return ModelRunnerOutput(...)
```

### 1.2 执行阶段分析

**关键发现**: EPLB负载平衡发生在**每次推理step完成后**，无论是prefill还是decode阶段。

```python
# 源码位置: vllm/v1/worker/gpu_model_runner.py:1189-1205
def eplb_step(self, is_dummy: bool = False, is_profile: bool = False) -> None:
    """Step for the EPLB (Expert Parallelism Load Balancing) state."""
    if not self.parallel_config.enable_eplb:
        return

    assert self.eplb_state is not None
    assert is_mixture_of_experts(self.model)
    
    # 调用EPLB状态的step方法
    self.eplb_state.step(
        self.model,
        is_dummy,
        is_profile,
        log_stats=self.parallel_config.eplb_log_balancedness,
    )
```

**执行时序**:
1. **Prefill阶段**: 每个prefill batch完成后执行EPLB step
2. **Decode阶段**: 每个decode step完成后执行EPLB step
3. **混合阶段**: 每个包含prefill+decode的step完成后执行EPLB step

## 2. 负载平衡频率配置

### 2.1 关键配置参数

**源码位置**: `vllm/config.py:1805-1818`

```python
class ParallelConfig:
    # 负载记录窗口大小
    eplb_window_size: int = 1000
    """Window size for expert load recording."""
    
    # 专家重排间隔步数  
    eplb_step_interval: int = 3000
    """
    Interval for rearranging experts in expert parallelism.
    
    Note that if this is greater than the EPLB window size, only the metrics
    of the last `eplb_window_size` steps will be used for rearranging experts.
    """
    
    # 是否记录负载均衡度
    eplb_log_balancedness: bool = False
    """
    Log the balancedness each step of expert parallelism.
    This is turned off by default since it will cause communication overhead.
    """
```

### 2.2 频率控制逻辑

**源码位置**: `vllm/distributed/eplb/eplb_state.py:328-336`

```python
def step(self, model, is_dummy=False, is_profile=False, log_stats=False):
    # 1. 每个step都更新负载统计窗口
    if not is_dummy:
        self.expert_load_window[self.expert_load_window_step] = (
            self.expert_load_pass.clone())
        self.expert_load_window_step += 1
        if self.expert_load_window_step >= self.expert_load_window_size:
            self.expert_load_window_step = 0  # 循环窗口
        self.expert_load_pass.zero_()

    # 2. 专家重排频率控制
    self.expert_rearrangement_step += 1
    if (self.expert_rearrangement_step >= self.expert_rearrangement_step_interval):
        self.expert_rearrangement_step = 0
        self.rearrange(model)  # 🔥 触发专家重排
```

**频率总结**:
- **负载统计更新**: 每个step（默认每次推理）
- **专家重排执行**: 每3000个step（可配置）
- **滑动窗口**: 保留最近1000个step的负载数据（可配置）

## 3. 负载平衡判断参数

### 3.1 核心数据结构

**源码位置**: `vllm/distributed/eplb/eplb_state.py:47-147`

```python
@dataclass
class EplbState:
    # 专家映射关系
    physical_to_logical_map: torch.Tensor    # (num_moe_layers, num_physical_experts)
    logical_to_physical_map: torch.Tensor    # (num_moe_layers, num_logical_experts, num_redundant_experts + 1)
    logical_replica_count: torch.Tensor      # (num_moe_layers, num_logical_experts)
    
    # 🔥 负载统计核心参数
    expert_load_pass: torch.Tensor           # (num_moe_layers, num_local_physical_experts)
    """当前pass中每个专家处理的token数量"""
    
    expert_load_window: torch.Tensor         # (window_size, num_moe_layers, num_local_physical_experts)  
    """滑动窗口中的历史负载数据"""
    
    # 控制参数
    expert_load_window_step: int = 0         # 当前窗口步数
    expert_load_window_size: int = 0         # 窗口大小(1000)
    expert_rearrangement_step: int = 0       # 当前重排步数
    expert_rearrangement_step_interval: int = 0  # 重排间隔(3000)
```

### 3.2 负载均衡度计算

**源码位置**: `vllm/distributed/eplb/eplb_state.py:288-317`

```python
def step(self, model, is_dummy=False, is_profile=False, log_stats=False):
    if log_stats:
        # 1. 收集当前step的负载统计
        num_tokens = self.expert_load_pass.sum(dim=-1)  # 每层的总token数
        
        # 2. 跨所有EP rank收集负载数据
        ep_group = get_ep_group().device_group
        num_tokens_list = [torch.empty_like(num_tokens) for _ in range(ep_group.size())]
        all_gather(num_tokens_list, num_tokens, group=ep_group)
        num_tokens_per_rank = torch.stack(num_tokens_list).float()  # (num_ranks, num_moe_layers)
        
        # 3. 计算负载均衡度指标
        avg_tokens_tensor = num_tokens_per_rank.mean(dim=0).sum(dim=0)  # 平均负载
        max_tokens_tensor = num_tokens_per_rank.max(dim=0).values.sum(dim=0)  # 最大负载
        
        # 🔥 关键指标: 负载均衡度 = 平均负载 / 最大负载
        balancedness = avg_tokens / max_tokens if max_tokens > 0 else 0.0
        
        # 4. 记录统计信息
        if ep_group.rank() == 0:
            logger.info("EPLB step: avg_tokens=%.2f, max_tokens=%d, balancedness=%.4f", 
                       avg_tokens, max_tokens, balancedness)
```

**判断参数说明**:
- `avg_tokens`: 所有EP rank的平均负载
- `max_tokens`: 所有EP rank的最大负载  
- `balancedness`: 负载均衡度，范围[0,1]，越接近1越均衡
- `expert_load_window`: 用于重排决策的历史负载数据

## 4. 负载统计参数获取方式

### 4.1 负载统计收集

**源码位置**: `vllm/model_executor/layers/fused_moe/layer.py:1258-1286`

```python
def select_experts(hidden_states, router_logits, top_k, enable_eplb=False,
                   expert_load_view=None, logical_to_physical_map=None, ...):
    
    if enable_eplb:
        # 1. 标准专家选择
        topk_weights, topk_ids = fused_topk(hidden_states, router_logits, top_k)
        
        # 2. 逻辑专家ID转换为物理专家ID
        physical_expert_ids = logical_to_physical_map[topk_ids]
        
        # 3. 🔥 关键: 负载统计累积
        # expert_load_view: (num_local_physical_experts,)
        invalid_mask = topk_ids_flatten < 0  # 过滤无效专家
        index = topk_ids_flatten.masked_fill_(invalid_mask, 0)
        src = ~invalid_mask  # 有效mask
        
        # 使用scatter_add_累积每个专家被选择的次数
        expert_load_view.scatter_add_(
            dim=0,
            index=index.long(), 
            src=src.to(expert_load_view)
        )
        
        return topk_weights, physical_expert_ids
```

### 4.2 负载数据传递链路

```python
# 1. MoE层前向传播时收集负载
# 源码位置: vllm/model_executor/layers/fused_moe/layer.py:1378-1381
def forward(self, hidden_states, router_logits, top_k, renormalize):
    return self.quant_method.apply(
        layer=self,
        x=hidden_states,
        router_logits=router_logits,
        top_k=top_k,
        renormalize=renormalize,
        enable_eplb=self.enable_eplb,
        expert_load_view=self.expert_load_view,  # 🔥 传递负载视图
        logical_to_physical_map=self.logical_to_physical_map,
        logical_replica_count=self.logical_replica_count,
    )

# 2. 量化方法中调用专家选择
# 源码位置: vllm/model_executor/layers/quantization/fp8.py
def apply(self, layer, x, router_logits, top_k, renormalize, **kwargs):
    topk_weights, topk_ids = layer.select_experts(
        hidden_states=x,
        router_logits=router_logits,
        top_k=top_k,
        renormalize=renormalize,
        enable_eplb=kwargs.get('enable_eplb', False),
        expert_load_view=kwargs.get('expert_load_view'),  # 🔥 负载统计传递
        logical_to_physical_map=kwargs.get('logical_to_physical_map'),
        logical_replica_count=kwargs.get('logical_replica_count')
    )

# 3. EPLB状态设置
# 源码位置: vllm/model_executor/layers/fused_moe/layer.py:1140-1155  
def set_eplb_state(self, moe_layer_idx, expert_load_view, 
                   logical_to_physical_map, logical_replica_count):
    """Register the EPLB state in this layer."""
    self.expert_load_view = expert_load_view[moe_layer_idx]  # 🔥 设置负载视图
    self.logical_to_physical_map = logical_to_physical_map[moe_layer_idx]
    self.logical_replica_count = logical_replica_count[moe_layer_idx]
```

## 5. 专家重排决策逻辑

### 5.1 重排触发条件

**源码位置**: `vllm/distributed/eplb/eplb_state.py:332-336`

```python
# 每个step都会检查是否需要重排
self.expert_rearrangement_step += 1
if (self.expert_rearrangement_step >= self.expert_rearrangement_step_interval):
    self.expert_rearrangement_step = 0
    self.rearrange(model)  # 🔥 触发专家重排
```

**触发条件**: 
- 累积步数达到 `eplb_step_interval`（默认3000步）
- 无论负载是否均衡都会执行（保证同步）

### 5.2 重排算法输入

**源码位置**: `vllm/distributed/eplb/eplb_state.py:366-408`

```python
def rearrange(self, model, is_profile=False):
    # 1. 将本地物理专家负载映射到全局逻辑专家
    logical_expert_load_window = torch.zeros(
        self.expert_load_window_size,      # 窗口大小: 1000
        model.num_moe_layers,              # MoE层数
        model.num_logical_experts,         # 逻辑专家数
        dtype=self.expert_load_window.dtype,
        device=self.expert_load_window.device,
    )
    
    # 2. 聚合所有EP rank的负载数据
    logical_expert_load_window.scatter_add_(
        dim=-1,
        index=local_physical_to_logical_map.unsqueeze(0).expand_as(self.expert_load_window).long(),
        src=self.expert_load_window,  # 🔥 使用滑动窗口中的历史负载
    )
    
    # 3. 跨rank同步负载数据
    global_expert_load_window = logical_expert_load_window.sum(dim=0)
    all_reduce(global_expert_load_window, group=ep_group)
    
    # 4. 调用重排算法
    (new_physical_to_logical_map, new_logical_to_physical_map, new_logical_replica_count) = (
        rebalance_experts(
            global_expert_load_window,  # 🔥 输入: 全局专家负载统计
            num_replicas,               # 物理专家总数
            num_groups,                 # 专家组数
            num_nodes,                  # 节点数
            num_gpus,                   # GPU数
        )
    )
```

## 6. 总结

### 6.1 执行时机
- **每个推理step完成后**都会调用EPLB step
- **无论prefill还是decode阶段**都会执行负载统计更新
- **专家重排**每3000步执行一次（可配置）

### 6.2 关键参数
- `expert_load_pass`: 当前step中每个专家的token处理数量
- `expert_load_window`: 滑动窗口中的历史负载数据（1000步）
- `balancedness`: 负载均衡度指标（平均负载/最大负载）

### 6.3 获取方式
- 通过专家选择过程中的`scatter_add_`操作累积负载统计
- 使用滑动窗口保存历史数据用于重排决策
- 通过all-reduce在所有EP rank间同步负载信息

### 6.4 平衡策略
- 基于历史负载统计进行专家重新分布
- 为热门专家创建更多副本，减少冷门专家副本
- 使用层次化或全局负载均衡算法优化分布
