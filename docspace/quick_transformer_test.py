#!/usr/bin/env python3
"""
快速测试：基于GitHub issue #1603，测试transformers版本是否能解决FX tracing问题
"""

import subprocess
import sys
import os

def check_current_version():
    """检查当前transformers版本"""
    try:
        import transformers
        print(f"当前transformers版本: {transformers.__version__}")
        return transformers.__version__
    except ImportError:
        print("transformers未安装")
        return None

def install_and_test_version(version):
    """安装指定版本并测试"""
    print(f"\n=== 测试 transformers {version} ===")
    
    # 1. 安装指定版本
    try:
        cmd = ["uv", "pip", "install", f"transformers=={version}", "--force-reinstall"]
        print(f"执行: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✅ 安装成功")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ 未找到uv命令，尝试使用pip")
        try:
            cmd = ["pip", "install", f"transformers=={version}", "--force-reinstall"]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("✅ pip安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ pip安装也失败: {e.stderr}")
            return False
    
    # 2. 重启Python进程来测试新版本
    test_script = f"""
import sys
sys.path.insert(0, '/workspace')

try:
    import transformers
    print(f"实际版本: {{transformers.__version__}}")
    
    # 测试原始代码的关键部分
    import torch
    from transformers import AutoTokenizer, AutoModelForCausalLM
    from llmcompressor.modifiers.smoothquant import SmoothQuantModifier
    
    print("✅ 模块导入成功")
    
    # 尝试创建SmoothQuantModifier
    modifier = SmoothQuantModifier(smoothing_strength=0.8)
    print("✅ SmoothQuantModifier创建成功")
    
    print("SUCCESS")
    
except Exception as e:
    print(f"❌ 错误: {{e}}")
    print("FAILED")
"""
    
    # 写入临时测试文件
    with open('/tmp/test_transformers.py', 'w') as f:
        f.write(test_script)
    
    # 运行测试
    try:
        result = subprocess.run([sys.executable, '/tmp/test_transformers.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if "SUCCESS" in result.stdout:
            print("✅ 基础测试通过")
            return True
        else:
            print("❌ 基础测试失败")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False

def run_full_test():
    """运行完整的SmoothQuant测试"""
    print("\n=== 运行完整SmoothQuant测试 ===")
    
    test_script = """
import os
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor.transformers import oneshot
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier

try:
    # 模型路径
    MODEL_ID = "/home/<USER>/single_llama"
    
    print("加载模型...")
    model = AutoModelForCausalLM.from_pretrained(
        MODEL_ID, device_map="auto", torch_dtype="auto",
    )
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    print("准备数据集...")
    # 使用很小的数据集进行快速测试
    NUM_CALIBRATION_SAMPLES = 16
    MAX_SEQUENCE_LENGTH = 256
    
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(NUM_CALIBRATION_SAMPLES))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    ds = ds.map(preprocess)
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=MAX_SEQUENCE_LENGTH, truncation=True, add_special_tokens=False)
    
    ds = ds.map(tokenize, remove_columns=ds.column_names)
    
    print("开始量化...")
    # 原始配置，不使用任何修复
    recipe = [
        SmoothQuantModifier(smoothing_strength=0.8),
        GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
    ]
    
    oneshot(
        model=model,
        dataset=ds,
        recipe=recipe,
        max_seq_length=MAX_SEQUENCE_LENGTH,
        num_calibration_samples=NUM_CALIBRATION_SAMPLES,
    )
    
    print("FULL_SUCCESS")
    
except Exception as e:
    print(f"FULL_FAILED: {e}")
    if "symbolically traced variables cannot be used as inputs to control flow" in str(e):
        print("CONFIRMED_FX_ERROR")
"""
    
    with open('/tmp/test_full_smoothquant.py', 'w') as f:
        f.write(test_script)
    
    try:
        result = subprocess.run([sys.executable, '/tmp/test_full_smoothquant.py'], 
                              capture_output=True, text=True, timeout=300)
        
        if "FULL_SUCCESS" in result.stdout:
            print("✅ 完整测试成功！")
            return True
        elif "CONFIRMED_FX_ERROR" in result.stdout:
            print("❌ 确认是FX tracing错误")
            return False
        else:
            print("❌ 完整测试失败")
            print("输出:", result.stdout[-500:])  # 只显示最后500字符
            print("错误:", result.stderr[-500:])
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 完整测试超时")
        return False

def main():
    """主函数"""
    print("Transformers版本FX Tracing问题调试")
    print("基于GitHub issue: https://github.com/vllm-project/llm-compressor/issues/1603")
    print("=" * 70)
    
    # 检查当前版本
    current_version = check_current_version()
    
    # 要测试的关键版本
    # 基于issue分析，问题可能在4.50+版本引入
    test_versions = [
        "4.45.2",  # 较早的稳定版本
        "4.46.3",  # 
        "4.47.1",  # 
        "4.48.1",  # 
        "4.49.0",  # 
        "4.50.0",  # 可能的问题引入版本
        "4.51.0",  # 
        "4.52.0",  # 
    ]
    
    successful_versions = []
    
    for version in test_versions:
        if install_and_test_version(version):
            # 基础测试通过，进行完整测试
            if run_full_test():
                successful_versions.append(version)
                print(f"🎉 transformers {version} 完全兼容!")
                break  # 找到第一个工作的版本就停止
            else:
                print(f"⚠️ transformers {version} 基础测试通过但完整测试失败")
        else:
            print(f"❌ transformers {version} 基础测试失败")
    
    # 结果总结
    print(f"\n{'='*70}")
    print("测试结果")
    print(f"{'='*70}")
    
    if successful_versions:
        recommended = successful_versions[0]
        print(f"✅ 找到兼容版本: transformers {recommended}")
        print(f"\n🔧 修复命令:")
        print(f"uv pip install transformers=={recommended}")
        print(f"# 或者使用pip:")
        print(f"pip install transformers=={recommended}")
        
        # 恢复到推荐版本
        print(f"\n正在安装推荐版本 {recommended}...")
        install_and_test_version(recommended)
        
    else:
        print("❌ 未找到兼容的transformers版本")
        print("\n💡 建议:")
        print("1. 继续使用环境变量修复方案")
        print("2. 或者尝试更早的transformers版本 (4.40.x)")
        
        # 恢复原版本
        if current_version:
            print(f"\n恢复到原版本 {current_version}...")
            install_and_test_version(current_version)

if __name__ == "__main__":
    main()
