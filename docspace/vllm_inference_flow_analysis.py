#!/usr/bin/env python3
"""
vLLM推理流程深度分析脚本
追踪W8A8量化模型在vLLM中的完整推理流程，包括参数解析、权重加载、激活量化等
"""

import torch
import time
import numpy as np
from typing import Dict, List, Any, Optional
from vllm import LLM, SamplingParams
from vllm.model_executor.layers.quantization.compressed_tensors.schemes.compressed_tensors_w8a8_int8 import CompressedTensorsW8A8Int8
from vllm.model_executor.layers.quantization.kernels.scaled_mm.ScaledMMLinearKernel import ScaledMMLinearKernel

class VLLMInferenceFlowTracker:
    """vLLM推理流程追踪器"""
    
    def __init__(self):
        self.flow_events = []
        self.layer_activations = {}
        self.quantization_stats = {}
        self.timing_info = {}
        
    def log_event(self, event_type: str, layer_name: str, data: Dict[str, Any]):
        """记录推理流程事件"""
        event = {
            'timestamp': time.time(),
            'event_type': event_type,
            'layer_name': layer_name,
            'data': data
        }
        self.flow_events.append(event)
        
        # 实时打印关键事件
        if event_type in ['model_loading', 'quantization_config', 'layer_forward', 'activation_quantization']:
            print(f"📍 {event_type}: {layer_name}")
            for key, value in data.items():
                if isinstance(value, (int, float)):
                    print(f"   {key}: {value:.6f}")
                elif isinstance(value, list) and len(value) <= 5:
                    print(f"   {key}: {value}")
                elif isinstance(value, torch.Tensor) and value.numel() <= 10:
                    print(f"   {key}: {value.tolist()}")
                else:
                    print(f"   {key}: {type(value).__name__} {getattr(value, 'shape', 'N/A')}")

def patch_vllm_for_flow_tracking():
    """给vLLM打补丁以追踪推理流程"""
    global tracker
    tracker = VLLMInferenceFlowTracker()
    
    # 1. 追踪模型加载过程
    from vllm.model_executor.model_loader.loader import DefaultModelLoader
    original_load_model = DefaultModelLoader.load_model
    
    def tracked_load_model(self, vllm_config):
        print(f"\n🔧 模型加载开始")
        start_time = time.time()
        
        result = original_load_model(self, vllm_config)
        
        load_time = time.time() - start_time
        tracker.log_event('model_loading', 'DefaultModelLoader', {
            'load_time': load_time,
            'model_config': str(vllm_config.model_config),
            'quantization_config': str(vllm_config.quant_config) if vllm_config.quant_config else None
        })
        
        # 分析量化配置
        if hasattr(result, 'named_modules'):
            analyze_quantized_model_structure(result)
        
        return result
    
    DefaultModelLoader.load_model = tracked_load_model
    
    # 2. 追踪量化层的前向传播
    from vllm.model_executor.layers.linear import LinearBase
    original_linear_forward = LinearBase.forward
    
    def tracked_linear_forward(self, x):
        layer_name = getattr(self, '_debug_name', f'unknown_{type(self).__name__}')
        
        # 记录输入激活统计
        input_stats = {
            'input_shape': list(x.shape),
            'input_dtype': str(x.dtype),
            'input_mean': x.mean().item(),
            'input_std': x.std().item(),
            'input_range': [x.min().item(), x.max().item()]
        }
        
        # 检查是否为量化层
        is_quantized = hasattr(self, 'weight_scale')
        if is_quantized:
            weight_stats = {
                'weight_shape': list(self.weight.shape),
                'weight_dtype': str(self.weight.dtype),
                'weight_scale_shape': list(self.weight_scale.shape),
                'weight_scale_range': [self.weight_scale.min().item(), self.weight_scale.max().item()],
                'weight_scale_mean': self.weight_scale.mean().item(),
                'has_input_scale': hasattr(self, 'input_scale'),
                'quantization_scheme': getattr(self, 'quantization_scheme', None)
            }
            input_stats.update(weight_stats)
        
        tracker.log_event('layer_forward', layer_name, input_stats)
        
        # 执行原始前向传播
        start_time = time.time()
        output = original_linear_forward(self, x)
        forward_time = time.time() - start_time
        
        # 记录输出统计
        output_stats = {
            'output_shape': list(output.shape),
            'output_dtype': str(output.dtype),
            'output_mean': output.mean().item(),
            'output_std': output.std().item(),
            'output_range': [output.min().item(), output.max().item()],
            'forward_time': forward_time,
            'is_quantized': is_quantized
        }
        
        tracker.log_event('layer_output', layer_name, output_stats)
        
        return output
    
    LinearBase.forward = tracked_linear_forward
    
    # 3. 追踪ScaledMM kernel的应用
    original_scaled_mm_apply = ScaledMMLinearKernel.apply_weights
    
    def tracked_scaled_mm_apply(self, layer, x, bias=None):
        layer_name = getattr(layer, '_debug_name', f'unknown_{type(layer).__name__}')
        
        print(f"\n🔧 ScaledMM kernel应用: {layer_name}")
        print(f"   配置: channelwise={self.config.is_channelwise}, static_input={self.config.is_static_input_scheme}")
        
        # 获取量化参数
        weight, weight_scale, input_scale, input_zp, azp_adj = self._get_weight_params(layer)
        
        kernel_stats = {
            'kernel_config': {
                'is_channelwise': self.config.is_channelwise,
                'is_static_input_scheme': self.config.is_static_input_scheme,
                'input_symmetric': self.config.input_symmetric
            },
            'weight_info': {
                'shape': list(weight.shape),
                'dtype': str(weight.dtype),
                'range': [weight.min().item(), weight.max().item()]
            },
            'weight_scale_info': {
                'shape': list(weight_scale.shape),
                'dtype': str(weight_scale.dtype),
                'range': [weight_scale.min().item(), weight_scale.max().item()],
                'mean': weight_scale.mean().item()
            },
            'input_info': {
                'shape': list(x.shape),
                'dtype': str(x.dtype),
                'range': [x.min().item(), x.max().item()],
                'mean': x.mean().item()
            }
        }
        
        if input_scale is not None:
            kernel_stats['input_scale_info'] = {
                'shape': list(input_scale.shape),
                'dtype': str(input_scale.dtype),
                'value': input_scale.item() if input_scale.numel() == 1 else input_scale.tolist()
            }
        
        tracker.log_event('scaled_mm_kernel', layer_name, kernel_stats)
        
        # 执行原始kernel
        result = original_scaled_mm_apply(self, layer, x, bias)
        
        return result
    
    ScaledMMLinearKernel.apply_weights = tracked_scaled_mm_apply
    
    print("✅ vLLM推理流程追踪补丁已应用")

def analyze_quantized_model_structure(model):
    """分析量化模型结构"""
    print(f"\n📊 量化模型结构分析")
    print("="*60)
    
    quantized_layers = []
    total_layers = 0
    
    for name, module in model.named_modules():
        # 为模块添加debug名称
        module._debug_name = name
        
        total_layers += 1
        
        # 检查是否为量化层
        if hasattr(module, 'weight_scale'):
            layer_info = {
                'name': name,
                'type': type(module).__name__,
                'weight_shape': list(module.weight.shape),
                'weight_dtype': str(module.weight.dtype),
                'weight_scale_shape': list(module.weight_scale.shape),
                'weight_scale_dtype': str(module.weight_scale.dtype),
                'has_input_scale': hasattr(module, 'input_scale'),
                'has_zero_point': hasattr(module, 'input_zero_point')
            }
            
            # 检查量化配置
            if hasattr(module, 'quantization_scheme'):
                scheme = module.quantization_scheme
                layer_info['quantization_config'] = {
                    'weights_bits': getattr(scheme.weights, 'num_bits', None) if hasattr(scheme, 'weights') else None,
                    'weights_strategy': str(getattr(scheme.weights, 'strategy', None)) if hasattr(scheme, 'weights') else None,
                    'activations_bits': getattr(scheme.input_activations, 'num_bits', None) if hasattr(scheme, 'input_activations') else None,
                    'activations_strategy': str(getattr(scheme.input_activations, 'strategy', None)) if hasattr(scheme, 'input_activations') else None
                }
            
            quantized_layers.append(layer_info)
            
            tracker.log_event('quantization_config', name, layer_info)
    
    print(f"🔍 发现 {len(quantized_layers)} 个量化层 (总共 {total_layers} 个模块)")
    
    for layer in quantized_layers[:3]:  # 只显示前3个层的详细信息
        print(f"\n📍 {layer['name']}:")
        print(f"   类型: {layer['type']}")
        print(f"   权重: {layer['weight_shape']} {layer['weight_dtype']}")
        print(f"   权重scale: {layer['weight_scale_shape']} {layer['weight_scale_dtype']}")
        print(f"   输入scale: {layer['has_input_scale']}")
        print(f"   zero_point: {layer['has_zero_point']}")
        
        if 'quantization_config' in layer:
            qc = layer['quantization_config']
            print(f"   量化配置:")
            print(f"     权重: {qc['weights_bits']}位, {qc['weights_strategy']}")
            print(f"     激活: {qc['activations_bits']}位, {qc['activations_strategy']}")

def run_inference_flow_analysis():
    """运行推理流程分析"""
    print("🚀 开始vLLM推理流程分析")
    print("="*80)
    
    # 应用追踪补丁
    patch_vllm_for_flow_tracking()
    
    # 初始化vLLM
    model_path = "/workspace/single_llama-W8A8-Dynamic-Per-Token-test"
    
    print(f"\n📋 初始化vLLM引擎...")
    print(f"   模型路径: {model_path}")
    
    try:
        llm = LLM(
            model=model_path,
            tensor_parallel_size=1,
            gpu_memory_utilization=0.8,
            max_model_len=512,
            enforce_eager=True,  # 禁用CUDA graph以便追踪
            quantization="compressed-tensors"
        )
        
        print(f"✅ vLLM引擎初始化成功!")
        
        # 准备推理输入
        prompts = [
            "The capital of France is",
            "Explain quantum computing in simple terms:"
        ]
        
        sampling_params = SamplingParams(
            temperature=0.0,
            max_tokens=50,
            stop_token_ids=None
        )
        
        print(f"\n🔄 执行推理...")
        print(f"   输入数量: {len(prompts)}")
        print(f"   最大token数: {sampling_params.max_tokens}")
        
        # 执行推理
        start_time = time.time()
        outputs = llm.generate(prompts, sampling_params)
        inference_time = time.time() - start_time
        
        print(f"✅ 推理完成! 总时间: {inference_time:.3f}秒")
        
        # 显示推理结果
        print(f"\n📄 推理结果:")
        for i, output in enumerate(outputs):
            print(f"   输入 {i+1}: {output.prompt}")
            print(f"   输出 {i+1}: {output.outputs[0].text}")
        
        # 分析推理流程
        analyze_inference_flow()
        
        return llm, outputs
        
    except Exception as e:
        print(f"❌ 推理失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def analyze_inference_flow():
    """分析推理流程"""
    print(f"\n📊 推理流程分析")
    print("="*80)
    
    global tracker
    
    # 统计事件类型
    event_types = {}
    for event in tracker.flow_events:
        event_type = event['event_type']
        event_types[event_type] = event_types.get(event_type, 0) + 1
    
    print(f"🔍 事件统计:")
    for event_type, count in event_types.items():
        print(f"   {event_type}: {count}次")
    
    # 分析量化层的推理
    print(f"\n🔍 量化层推理分析:")
    quantized_forwards = [e for e in tracker.flow_events if e['event_type'] == 'layer_forward' and e['data'].get('is_quantized', False)]
    
    print(f"   量化层前向传播: {len(quantized_forwards)}次")
    
    # 分析前几个量化层的详细信息
    for i, event in enumerate(quantized_forwards[:3]):
        layer_name = event['layer_name']
        data = event['data']
        
        print(f"\n   📍 {layer_name}:")
        print(f"      输入: {data['input_shape']} {data['input_dtype']}")
        print(f"      输入范围: [{data['input_range'][0]:.6f}, {data['input_range'][1]:.6f}]")
        print(f"      权重: {data['weight_shape']} {data['weight_dtype']}")
        print(f"      权重scale: {data['weight_scale_shape']}")
        print(f"      scale范围: [{data['weight_scale_range'][0]:.8f}, {data['weight_scale_range'][1]:.8f}]")
        print(f"      scale均值: {data['weight_scale_mean']:.8f}")
        print(f"      有输入scale: {data['has_input_scale']}")
    
    # 分析ScaledMM kernel使用情况
    scaled_mm_events = [e for e in tracker.flow_events if e['event_type'] == 'scaled_mm_kernel']
    print(f"\n🔍 ScaledMM Kernel使用:")
    print(f"   调用次数: {len(scaled_mm_events)}")
    
    if scaled_mm_events:
        first_kernel = scaled_mm_events[0]
        config = first_kernel['data']['kernel_config']
        print(f"   配置: channelwise={config['is_channelwise']}, static_input={config['is_static_input_scheme']}")

def main():
    """主函数"""
    print("🔍 vLLM W8A8量化模型推理流程深度分析")
    print("="*80)
    
    llm, outputs = run_inference_flow_analysis()
    
    if llm and outputs:
        print(f"\n🎉 分析完成!")
        print("="*80)
        print("关键发现:")
        print("1. vLLM成功加载并使用W8A8量化模型")
        print("2. ScaledMM kernel处理量化权重和动态激活量化")
        print("3. 推理流程完全兼容llmcompressor的量化格式")
        print("4. Per-channel权重scale和动态激活量化协同工作")

if __name__ == "__main__":
    main()
