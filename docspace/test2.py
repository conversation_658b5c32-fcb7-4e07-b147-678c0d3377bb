
import os
import torch
import logging
from vllm import LLM, SamplingParams

# 🔧 CUDA Device-Side Assert 错误解决方案
# 设置关键环境变量来解决CUDA device-side assert错误
# os.environ['CUDA_LAUNCH_BLOCKING'] = '1'  # 同步CUDA调用以获得准确的错误位置
# os.environ['TORCH_USE_CUDA_DSA'] = '1'    # 启用设备端断言以获得更详细的错误信息
# os.environ['VLLM_USE_V1'] = '0'           # 🎯 关键：强制使用V0引擎，避免V1引擎的内存问题
# os.environ['VLLM_USE_TRITON_FLASH_ATTN'] = '0'  # 禁用Triton Flash Attention

# 设置详细日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_cuda_environment():
    """检查CUDA环境和GPU状态"""
    print("=== CUDA环境检查 ===")

    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return False

    print(f"✅ CUDA版本: {torch.version.cuda}")
    print(f"✅ PyTorch版本: {torch.__version__}")
    print(f"✅ 可用GPU数量: {torch.cuda.device_count()}")

    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        total_memory = props.total_memory / 1024**3
        allocated = torch.cuda.memory_allocated(i) / 1024**3
        print(f"GPU {i}: {props.name}")
        print(f"  总内存: {total_memory:.2f} GB")
        print(f"  已分配: {allocated:.2f} GB")
        print(f"  计算能力: {props.major}.{props.minor}")

    return True

def debug_model_loading():
    """调试模型加载和kernel调用流程"""

    # 检查CUDA环境
    if not check_cuda_environment():
        return

    # 清理GPU内存
    torch.cuda.empty_cache()
    print("✅ GPU内存已清理")

    # 断点1: 开始模型初始化
    print("\n=== 开始模型初始化 ===")

    model_name = "/home/<USER>/single_llama"
    # Qwen-QwQ-32B-W8A8-SmoothQuant Meta-Llama-3-8B-Instruct-GPTQ-Int8

    # 这里设置第一个断点 - 模型初始化入口
    # 根据可用GPU数量动态生成配置
    num_available_gpus = 4

    configs = []

    # 如果有多张GPU，优先尝试多GPU配置
    # if num_available_gpus >= 2:
    #     configs.extend([
    #         {
    #             "name": f"多GPU配置 ({min(num_available_gpus, 4)}张卡)",
    #             "tensor_parallel_size": min(num_available_gpus, 4),
    #             "gpu_memory_utilization": 0.85,
    #             "max_model_len": 1024,
    #             "enable_prefix_caching": True,
    #         },
    #         {
    #             "name": f"保守多GPU配置 ({min(num_available_gpus, 2)}张卡)",
    #             "tensor_parallel_size": min(num_available_gpus, 2),
    #             "gpu_memory_utilization": 0.75,
    #             "max_model_len": 512,
    #             "enable_prefix_caching": False,
    #         }
    #     ])

    # 🎯 优化后的配置，专门解决CUDA device-side assert错误
    configs.extend([
        {
            "name": "✅ 推荐配置: V0引擎优化",
            "tensor_parallel_size": 1,
            "gpu_memory_utilization": 0.8,  # 提高内存利用率
            "max_model_len": 512,             # 适中的序列长度
            "enable_prefix_caching": False,
            # "block_size": 16,
            # "enforce_eager": True,           # 禁用CUDA图，提高稳定性
            # "disable_custom_all_reduce": True,
            # "max_num_seqs": 1,              # 单序列处理
        }
        # ，
        # {
        #     "name": "备选配置1: 保守内存",
        #     "tensor_parallel_size": 1,
        #     "gpu_memory_utilization": 0.7,
        #     "max_model_len": 32,
        #     "enable_prefix_caching": False,
        #     "block_size": 8,
        #     "enforce_eager": True,
        #     "disable_custom_all_reduce": True,
        #     "max_num_seqs": 1,
        # },
        # {
        #     "name": "备选配置2: 标准配置",
        #     "tensor_parallel_size": 1,
        #     "gpu_memory_utilization": 0.85,
        #     "max_model_len": 128,
        #     "enable_prefix_caching": False,
        #     "block_size": 16,
        #     "enforce_eager": True,
        #     "disable_custom_all_reduce": True,
        #     "max_num_seqs": 2,
        # }
    ])

    llm = None
    successful_config = None

    for config in configs:
        print(f"\n=== 尝试{config['name']} ===")
        print(f"tensor_parallel_size: {config['tensor_parallel_size']}")
        print(f"gpu_memory_utilization: {config['gpu_memory_utilization']}")
        print(f"max_model_len: {config['max_model_len']}")
        print(f"block_size: {config.get('block_size', 16)}")
        print(f"enforce_eager: {config.get('enforce_eager', False)}")

        try:
            # 清理GPU内存
            torch.cuda.empty_cache()
            print("GPU内存已清理")

            # 如果使用多GPU，设置可见的GPU
            if config['tensor_parallel_size'] > 1:
                visible_gpus = list(range(config['tensor_parallel_size']))
                os.environ['CUDA_VISIBLE_DEVICES'] = ','.join(map(str, visible_gpus))
                print(f"使用GPU: {visible_gpus}")

            # 构建LLM参数
            llm_kwargs = {
                "model": model_name,
                "tensor_parallel_size": config["tensor_parallel_size"],
                "max_model_len": config["max_model_len"],
                "enable_prefix_caching": config["enable_prefix_caching"],
                "trust_remote_code": True,
                "gpu_memory_utilization": config["gpu_memory_utilization"],
            }

            # 添加可选参数
            if "block_size" in config:
                llm_kwargs["block_size"] = config["block_size"]
            if "enforce_eager" in config:
                llm_kwargs["enforce_eager"] = config["enforce_eager"]
            if "disable_custom_all_reduce" in config:
                llm_kwargs["disable_custom_all_reduce"] = config["disable_custom_all_reduce"]
            if "max_num_seqs" in config:
                llm_kwargs["max_num_seqs"] = config["max_num_seqs"]

            print("开始加载模型...")
            llm = LLM(**llm_kwargs)
            print(f"✅ {config['name']}成功！")
            successful_config = config
            break

        except Exception as e:
            print(f"❌ {config['name']}失败: {str(e)}")
            # 打印更详细的错误信息
            import traceback
            print("详细错误信息:")
            traceback.print_exc()

            # 清理内存后继续尝试下一个配置
            torch.cuda.empty_cache()
            continue

    if llm is None:
        raise RuntimeError("所有配置都失败了，无法加载模型")
    
    print("=== 模型加载完成 ===")
    if successful_config:
        print(f"成功配置: {successful_config['name']}")
        print(f"使用GPU数量: {successful_config['tensor_parallel_size']}")
        print(f"内存利用率: {successful_config['gpu_memory_utilization']}")
        print(f"最大序列长度: {successful_config['max_model_len']}")

    # 断点2: 开始推理测试
    print("\n=== 开始推理测试 ===")

    # 使用非常简单的prompt来避免复杂的attention计算
    prompts = ["Hi"]
    sampling_params = SamplingParams(
        temperature=0.0,
        top_p=1.0,
        max_tokens=24  # 减少生成长度
        # 注意：移除了 use_beam_search 参数，因为在新版本中不支持
    )

    try:
        print("开始生成...")
        # 这里设置第二个断点 - 推理入口
        outputs = llm.generate(prompts, sampling_params)

        print("✅ 推理成功！")
        for output in outputs:
            print(f"输入: {output.prompt}")
            print(f"输出: {output.outputs[0].text}")

    except Exception as e:
        print(f"❌ 推理失败: {str(e)}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return

if __name__ == "__main__":
    debug_model_loading()
