#!/usr/bin/env python3
"""
Flash Attention 兼容性调试工具
专门用于调试 flash_attn 库的兼容性问题
"""

import os
import torch
import subprocess
import sys

# 设置CUDA调试环境变量
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ['TORCH_USE_CUDA_DSA'] = '1'

def check_versions():
    """检查关键库的版本"""
    print("=== 版本信息检查 ===")
    
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"cuDNN版本: {torch.backends.cudnn.version()}")
    
    # 检查CUDA是否可用
    if torch.cuda.is_available():
        print(f"CUDA设备数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            print(f"GPU {i}: {props.name} (计算能力: {props.major}.{props.minor})")
    else:
        print("❌ CUDA不可用")
        return False
    
    return True

def check_flash_attn_installation():
    """检查flash_attn安装状态"""
    print("\n=== Flash Attention 安装检查 ===")
    
    try:
        import flash_attn
        print(f"✅ flash_attn已安装，版本: {flash_attn.__version__}")
        
        # 检查具体的模块
        try:
            from flash_attn import flash_attn_func
            print("✅ flash_attn_func 可导入")
        except ImportError as e:
            print(f"❌ flash_attn_func 导入失败: {e}")
            return False
            
        try:
            import flash_attn_2_cuda
            print("✅ flash_attn_2_cuda 可导入")
        except ImportError as e:
            print(f"❌ flash_attn_2_cuda 导入失败: {e}")
            print("这通常表示flash_attn与PyTorch版本不兼容")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ flash_attn未安装: {e}")
        return False

def test_flash_attn_basic():
    """测试flash_attn基础功能"""
    print("\n=== Flash Attention 基础测试 ===")
    
    try:
        from flash_attn import flash_attn_func
        
        # 创建测试张量
        batch_size, seq_len, num_heads, head_dim = 1, 32, 8, 64
        
        q = torch.randn(batch_size, seq_len, num_heads, head_dim, 
                       device='cuda', dtype=torch.float16)
        k = torch.randn(batch_size, seq_len, num_heads, head_dim, 
                       device='cuda', dtype=torch.float16)
        v = torch.randn(batch_size, seq_len, num_heads, head_dim, 
                       device='cuda', dtype=torch.float16)
        
        print(f"输入张量形状: q={q.shape}, k={k.shape}, v={v.shape}")
        print(f"输入张量设备: {q.device}")
        print(f"输入张量类型: {q.dtype}")
        
        # 执行flash attention
        print("执行flash_attn_func...")
        out = flash_attn_func(q, k, v)
        
        print(f"✅ Flash Attention测试成功！输出形状: {out.shape}")
        return True
        
    except Exception as e:
        print(f"❌ Flash Attention测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_vllm_flash_attn():
    """检查vLLM中的flash_attn相关模块"""
    print("\n=== vLLM Flash Attention 检查 ===")
    
    try:
        # 检查vLLM的flash_attn模块
        import vllm.vllm_flash_attn
        print("✅ vllm.vllm_flash_attn 可导入")
        
        # 检查具体的接口
        from vllm.vllm_flash_attn.flash_attn_interface import flash_attn_varlen_func
        print("✅ flash_attn_varlen_func 可导入")
        
        return True
        
    except ImportError as e:
        print(f"❌ vLLM flash_attn模块导入失败: {e}")
        return False

def suggest_fixes():
    """建议修复方案"""
    print("\n=== 修复建议 ===")
    
    print("1. 重新安装兼容的flash_attn版本:")
    print("   pip uninstall flash-attn -y")
    print("   pip install flash-attn --no-build-isolation")
    
    print("\n2. 或者使用预编译版本:")
    print("   pip install flash-attn --find-links https://github.com/Dao-AILab/flash-attention/releases")
    
    print("\n3. 检查CUDA和PyTorch兼容性:")
    print("   确保CUDA版本与PyTorch版本兼容")
    
    print("\n4. 如果问题持续，尝试使用eager模式:")
    print("   在vLLM中设置 enforce_eager=True")
    
    print("\n5. 检查环境变量:")
    print("   export FLASH_ATTENTION_FORCE_COMPILE=1")

def main():
    """主函数"""
    print("Flash Attention 兼容性调试工具")
    print("=" * 50)
    
    # 检查基础版本
    if not check_versions():
        return False
    
    # 检查flash_attn安装
    flash_attn_ok = check_flash_attn_installation()
    
    if flash_attn_ok:
        # 测试基础功能
        if test_flash_attn_basic():
            print("✅ Flash Attention基础功能正常")
        else:
            print("❌ Flash Attention基础功能异常")
            flash_attn_ok = False
    
    # 检查vLLM集成
    vllm_flash_ok = check_vllm_flash_attn()
    
    if flash_attn_ok and vllm_flash_ok:
        print("\n🎉 Flash Attention环境检查通过！")
        print("可以尝试运行vLLM了")
        return True
    else:
        print("\n❌ Flash Attention环境有问题")
        suggest_fixes()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
