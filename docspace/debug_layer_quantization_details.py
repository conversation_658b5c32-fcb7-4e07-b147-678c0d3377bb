#!/usr/bin/env python3
"""
详细Debug脚本：追踪量化前后模型各层的映射处理方法
特别关注o_proj和down_proj层的量化过程
"""

import torch
import copy
import json
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier

class LayerQuantizationTracker:
    """层量化追踪器"""
    
    def __init__(self):
        self.layer_info = {}
        self.quantization_steps = []
        self.smoothquant_operations = []
        self.gptq_operations = []
        
    def capture_model_state(self, model, stage_name):
        """捕获模型状态"""
        state = {
            'stage': stage_name,
            'layers': {},
            'total_params': sum(p.numel() for p in model.parameters()),
            'param_count_by_dtype': {}
        }
        
        # 统计不同数据类型的参数
        dtype_counts = {}
        for name, param in model.named_parameters():
            dtype = str(param.dtype)
            if dtype not in dtype_counts:
                dtype_counts[dtype] = 0
            dtype_counts[dtype] += param.numel()
        state['param_count_by_dtype'] = dtype_counts
        
        # 捕获关键层信息
        target_layers = [
            'model.layers.0.self_attn.q_proj',
            'model.layers.0.self_attn.k_proj', 
            'model.layers.0.self_attn.v_proj',
            'model.layers.0.self_attn.o_proj',  # 重点关注
            'model.layers.0.mlp.gate_proj',
            'model.layers.0.mlp.up_proj',
            'model.layers.0.mlp.down_proj',     # 重点关注
            'model.layers.0.input_layernorm',
            'model.layers.0.post_attention_layernorm'
        ]
        
        for layer_name in target_layers:
            try:
                layer = self._get_layer_by_name(model, layer_name)
                if layer is not None:
                    layer_info = {
                        'weight_shape': list(layer.weight.shape) if hasattr(layer, 'weight') else None,
                        'weight_dtype': str(layer.weight.dtype) if hasattr(layer, 'weight') else None,
                        'bias_shape': list(layer.bias.shape) if hasattr(layer, 'bias') and layer.bias is not None else None,
                        'has_quantization_params': False,
                        'quantization_params': {}
                    }
                    
                    # 检查量化参数
                    for attr_name in dir(layer):
                        if 'weight_scale' in attr_name or 'weight_zero_point' in attr_name or 'weight_g_idx' in attr_name:
                            layer_info['has_quantization_params'] = True
                            attr_value = getattr(layer, attr_name)
                            if torch.is_tensor(attr_value):
                                layer_info['quantization_params'][attr_name] = {
                                    'shape': list(attr_value.shape),
                                    'dtype': str(attr_value.dtype),
                                    'device': str(attr_value.device)
                                }
                    
                    state['layers'][layer_name] = layer_info
            except Exception as e:
                print(f"Error capturing layer {layer_name}: {e}")
        
        self.layer_info[stage_name] = state
        return state
    
    def _get_layer_by_name(self, model, layer_name):
        """根据名称获取层"""
        try:
            parts = layer_name.split('.')
            current = model
            for part in parts:
                current = getattr(current, part)
            return current
        except:
            return None
    
    def track_smoothquant_operation(self, layer_name, operation_type, details):
        """追踪SmoothQuant操作"""
        self.smoothquant_operations.append({
            'layer_name': layer_name,
            'operation_type': operation_type,
            'details': details,
            'timestamp': torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
        })
    
    def track_gptq_operation(self, layer_name, operation_type, details):
        """追踪GPTQ操作"""
        self.gptq_operations.append({
            'layer_name': layer_name,
            'operation_type': operation_type,
            'details': details,
            'timestamp': torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
        })
    
    def compare_layers(self, stage1, stage2, layer_name):
        """比较两个阶段的层变化"""
        if stage1 not in self.layer_info or stage2 not in self.layer_info:
            return None
        
        layer1 = self.layer_info[stage1]['layers'].get(layer_name)
        layer2 = self.layer_info[stage2]['layers'].get(layer_name)
        
        if not layer1 or not layer2:
            return None
        
        comparison = {
            'layer_name': layer_name,
            'weight_dtype_change': f"{layer1['weight_dtype']} -> {layer2['weight_dtype']}",
            'quantization_added': layer2['has_quantization_params'] and not layer1['has_quantization_params'],
            'new_quantization_params': layer2['quantization_params'] if layer2['has_quantization_params'] else {}
        }
        
        return comparison

def patch_smoothquant_for_tracking(tracker):
    """给SmoothQuant打补丁以追踪操作"""
    import llmcompressor.modifiers.smoothquant.base as smoothquant_module
    
    original_apply_smoothing = smoothquant_module.SmoothQuantModifier._apply_smoothing
    original_calculate_scales = smoothquant_module.SmoothQuantModifier._calculate_smoothing_scales
    
    def tracked_apply_smoothing(self, model):
        print(f"\n🔧 SmoothQuant._apply_smoothing 开始执行")
        print(f"   位置: llmcompressor/modifiers/smoothquant/base.py:257-308")
        
        for mapping in self.resolved_mappings_:
            if mapping.smooth_name not in self.scales_:
                continue
                
            print(f"\n📍 处理映射: {mapping.smooth_name}")
            
            # 获取激活缩放因子
            activation_scales = (
                self.scales_[mapping.smooth_name].max_channel_vals
                - self.scales_[mapping.smooth_name].min_channel_vals
            )
            
            print(f"   激活缩放因子形状: {activation_scales.shape}")
            print(f"   激活缩放因子范围: [{activation_scales.min():.6f}, {activation_scales.max():.6f}]")
            
            # 计算平滑缩放因子
            scales = self._calculate_smoothing_scales(mapping.balance_layers, activation_scales)
            print(f"   平滑缩放因子形状: {scales.shape}")
            print(f"   平滑缩放因子范围: [{scales.min():.6f}, {scales.max():.6f}]")
            
            # 追踪每个balance层的变化
            for i, layer in enumerate(mapping.balance_layers):
                layer_name = None
                for name, module in model.named_modules():
                    if module is layer:
                        layer_name = name
                        break
                
                if layer_name:
                    print(f"\n   🎯 处理balance层: {layer_name}")
                    
                    # 记录权重变化前的状态
                    original_weight_stats = {
                        'mean': layer.weight.mean().item(),
                        'std': layer.weight.std().item(),
                        'min': layer.weight.min().item(),
                        'max': layer.weight.max().item()
                    }
                    
                    # 应用缩放
                    layer.weight.mul_(scales.view(1, -1))
                    
                    # 记录权重变化后的状态
                    new_weight_stats = {
                        'mean': layer.weight.mean().item(),
                        'std': layer.weight.std().item(),
                        'min': layer.weight.min().item(),
                        'max': layer.weight.max().item()
                    }
                    
                    print(f"     权重变化前: mean={original_weight_stats['mean']:.6f}, std={original_weight_stats['std']:.6f}")
                    print(f"     权重变化后: mean={new_weight_stats['mean']:.6f}, std={new_weight_stats['std']:.6f}")
                    print(f"     变化比例: mean={new_weight_stats['mean']/original_weight_stats['mean']:.6f}")
                    
                    tracker.track_smoothquant_operation(layer_name, 'weight_scaling', {
                        'original_stats': original_weight_stats,
                        'new_stats': new_weight_stats,
                        'scale_factor_applied': 'scales.view(1, -1)'
                    })
            
            # 处理smooth层
            smooth_layer = mapping.smooth_layer
            smooth_layer_name = None
            for name, module in model.named_modules():
                if module is smooth_layer:
                    smooth_layer_name = name
                    break
            
            if smooth_layer_name:
                print(f"\n   🎯 处理smooth层: {smooth_layer_name}")
                
                # 记录LayerNorm权重变化
                original_norm_stats = {
                    'mean': smooth_layer.weight.mean().item(),
                    'std': smooth_layer.weight.std().item(),
                    'min': smooth_layer.weight.min().item(),
                    'max': smooth_layer.weight.max().item()
                }
                
                # 应用缩放
                if smooth_layer.weight.ndim == 1:
                    smooth_layer.weight.div_(scales)
                else:
                    smooth_layer.weight.div_(scales.view(-1, 1))
                
                if hasattr(smooth_layer, "bias") and smooth_layer.bias is not None:
                    smooth_layer.bias.div_(scales)
                
                new_norm_stats = {
                    'mean': smooth_layer.weight.mean().item(),
                    'std': smooth_layer.weight.std().item(),
                    'min': smooth_layer.weight.min().item(),
                    'max': smooth_layer.weight.max().item()
                }
                
                print(f"     LayerNorm权重变化前: mean={original_norm_stats['mean']:.6f}")
                print(f"     LayerNorm权重变化后: mean={new_norm_stats['mean']:.6f}")
                
                tracker.track_smoothquant_operation(smooth_layer_name, 'layernorm_scaling', {
                    'original_stats': original_norm_stats,
                    'new_stats': new_norm_stats,
                    'scale_factor_applied': 'div_(scales)'
                })
            
            # 清理校准数据
            del self.scales_[mapping.smooth_name]
    
    def tracked_calculate_scales(self, balance_layers, activation_scales):
        print(f"\n🧮 计算平滑缩放因子")
        print(f"   位置: llmcompressor/modifiers/smoothquant/base.py:310-338")
        print(f"   平滑强度α: {self.smoothing_strength}")
        
        # 获取权重缩放因子
        weight_scales = []
        for i, layer in enumerate(balance_layers):
            scale = layer.weight.abs().max(dim=0, keepdim=True)[0]
            weight_scales.append(scale)
            print(f"   层{i}权重缩放因子: shape={scale.shape}, range=[{scale.min():.6f}, {scale.max():.6f}]")
        
        weight_scales = 2.0 * torch.cat(weight_scales, dim=0).max(dim=0)[0]
        print(f"   合并权重缩放因子: shape={weight_scales.shape}, range=[{weight_scales.min():.6f}, {weight_scales.max():.6f}]")
        
        # 计算平滑因子: s_j = max(|X_j|)^α / max(|W_j|)^(1-α)
        scales = activation_scales.pow(self.smoothing_strength) / weight_scales.pow(1 - self.smoothing_strength)
        scales = torch.where(weight_scales > 0.0, scales, activation_scales)
        
        print(f"   最终平滑因子: shape={scales.shape}, range=[{scales.min():.6f}, {scales.max():.6f}]")
        
        return scales
    
    smoothquant_module.SmoothQuantModifier._apply_smoothing = tracked_apply_smoothing
    smoothquant_module.SmoothQuantModifier._calculate_smoothing_scales = tracked_calculate_scales

def patch_gptq_for_tracking(tracker):
    """给GPTQ打补丁以追踪操作"""
    import llmcompressor.modifiers.quantization.gptq.base as gptq_module
    
    original_compress_modules = gptq_module.GPTQModifier.compress_modules
    
    def tracked_compress_modules(self):
        print(f"\n🎯 GPTQ.compress_modules 开始执行")
        print(f"   位置: llmcompressor/modifiers/quantization/gptq/base.py:251-282")
        
        for module in list(self._num_samples.keys()):
            name = self._module_names[module]
            num_samples = self._num_samples[module]
            quant_args = getattr_chain(module, "quantization_scheme.weights")
            
            print(f"\n📍 量化层: {name}")
            print(f"   样本数: {num_samples}")
            print(f"   量化参数: {quant_args}")
            
            # 记录量化前的权重状态
            original_weight = module.weight.clone()
            original_stats = {
                'shape': list(original_weight.shape),
                'dtype': str(original_weight.dtype),
                'mean': original_weight.mean().item(),
                'std': original_weight.std().item(),
                'min': original_weight.min().item(),
                'max': original_weight.max().item()
            }
            
            print(f"   量化前权重: shape={original_stats['shape']}, dtype={original_stats['dtype']}")
            print(f"   量化前统计: mean={original_stats['mean']:.6f}, std={original_stats['std']:.6f}")
            print(f"   量化前范围: [{original_stats['min']:.6f}, {original_stats['max']:.6f}]")
            
            # 执行量化
            with torch.no_grad(), align_module_device(module), \
                 self._maybe_onload_hessian(module), CompressionLogger(module) as comp_logger:
                
                loss, quantized_weight, scale, zero_point, g_idx = quantize_weight(
                    module=module,
                    quant_args=quant_args,
                    hessians_dict=self._hessians,
                    blocksize=self.block_size,
                    percdamp=self.dampening_frac,
                )
                comp_logger.set_loss(loss)
            
            # 记录量化后的状态
            quantized_stats = {
                'shape': list(quantized_weight.shape),
                'dtype': str(quantized_weight.dtype),
                'mean': quantized_weight.float().mean().item(),
                'std': quantized_weight.float().std().item(),
                'min': quantized_weight.float().min().item(),
                'max': quantized_weight.float().max().item()
            }
            
            scale_stats = {
                'shape': list(scale.shape),
                'dtype': str(scale.dtype),
                'mean': scale.mean().item(),
                'std': scale.std().item(),
                'min': scale.min().item(),
                'max': scale.max().item()
            }
            
            zero_point_stats = {
                'shape': list(zero_point.shape),
                'dtype': str(zero_point.dtype),
                'mean': zero_point.float().mean().item(),
                'std': zero_point.float().std().item(),
                'min': zero_point.float().min().item(),
                'max': zero_point.float().max().item()
            }
            
            print(f"   量化后权重: shape={quantized_stats['shape']}, dtype={quantized_stats['dtype']}")
            print(f"   量化后统计: mean={quantized_stats['mean']:.6f}, std={quantized_stats['std']:.6f}")
            print(f"   量化后范围: [{quantized_stats['min']:.6f}, {quantized_stats['max']:.6f}]")
            
            print(f"   缩放因子: shape={scale_stats['shape']}, dtype={scale_stats['dtype']}")
            print(f"   缩放因子统计: mean={scale_stats['mean']:.6f}, range=[{scale_stats['min']:.6f}, {scale_stats['max']:.6f}]")
            
            print(f"   零点: shape={zero_point_stats['shape']}, dtype={zero_point_stats['dtype']}")
            print(f"   零点统计: mean={zero_point_stats['mean']:.6f}, range=[{zero_point_stats['min']:.6f}, {zero_point_stats['max']:.6f}]")
            
            print(f"   量化损失: {loss:.8f}")
            
            # 特别关注o_proj和down_proj
            if 'o_proj' in name or 'down_proj' in name:
                print(f"\n   🔍 特别关注层 {name}:")
                print(f"     原始权重形状: {original_weight.shape}")
                print(f"     量化权重形状: {quantized_weight.shape}")
                print(f"     缩放因子形状: {scale.shape}")
                print(f"     零点形状: {zero_point.shape}")
                
                # 计算量化公式验证
                if quantized_weight.dtype == torch.int8:
                    reconstructed = quantized_weight.float() * scale + zero_point.float()
                    reconstruction_error = (original_weight - reconstructed).abs().mean().item()
                    print(f"     重构误差: {reconstruction_error:.8f}")
            
            # 更新模块参数
            update_offload_parameter(module, "weight", quantized_weight)
            update_offload_parameter(module, "weight_scale", scale)
            update_offload_parameter(module, "weight_zero_point", zero_point)
            if g_idx is not None:
                update_offload_parameter(module, "weight_g_idx", g_idx)
            
            # 追踪操作
            tracker.track_gptq_operation(name, 'quantization', {
                'original_stats': original_stats,
                'quantized_stats': quantized_stats,
                'scale_stats': scale_stats,
                'zero_point_stats': zero_point_stats,
                'loss': loss,
                'block_size': self.block_size,
                'dampening_frac': self.dampening_frac
            })
            
            # 清理Hessian数据
            del self._num_samples[module]
    
    # 导入必要的函数
    from compressed_tensors.utils import align_module_device, getattr_chain, update_offload_parameter
    from llmcompressor.modifiers.quantization.gptq.gptq_quantize import quantize_weight
    from llmcompressor.utils.metric_logging import CompressionLogger
    
    gptq_module.GPTQModifier.compress_modules = tracked_compress_modules

def main():
    """主函数"""
    print("🔍 详细Debug: 层量化映射处理方法追踪")
    print("="*80)
    
    # 创建追踪器
    tracker = LayerQuantizationTracker()
    
    # 打补丁
    patch_smoothquant_for_tracking(tracker)
    patch_gptq_for_tracking(tracker)
    
    # 准备数据
    MODEL_ID = "/home/<USER>/single_llama"
    
    print("📥 加载模型和数据...")
    model = AutoModelForCausalLM.from_pretrained(
        MODEL_ID, device_map="auto", torch_dtype="auto"
    )
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    # 准备数据集
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(32))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=512, truncation=True, add_special_tokens=False)
    
    ds = ds.map(preprocess).map(tokenize, remove_columns=ds.column_names)
    
    # 创建recipe
    recipe = [
        SmoothQuantModifier(smoothing_strength=0.8),
        GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
    ]
    
    print("\n📊 捕获原始模型状态...")
    tracker.capture_model_state(model, "original")
    
    # 执行量化
    print("\n🚀 开始量化...")
    from llmcompressor.entrypoints.oneshot import oneshot
    
    quantized_model = oneshot(
        model=model,
        dataset=ds,
        recipe=recipe,
        max_seq_length=512,
        num_calibration_samples=32,
    )
    
    print("\n📊 捕获量化后模型状态...")
    tracker.capture_model_state(quantized_model, "quantized")
    
    # 生成报告
    generate_detailed_report(tracker)

def generate_detailed_report(tracker):
    """生成详细报告"""
    print("\n" + "="*80)
    print("📋 生成详细量化报告")
    print("="*80)

    # 1. 模型状态对比
    print("\n📊 模型状态对比:")
    for stage in tracker.layer_info:
        state = tracker.layer_info[stage]
        print(f"\n{stage.upper()}阶段:")
        print(f"  总参数数: {state['total_params']:,}")
        print(f"  数据类型分布: {state['param_count_by_dtype']}")

    # 2. 层级变化分析
    print("\n🔍 关键层变化分析:")
    target_layers = [
        'model.layers.0.self_attn.o_proj',
        'model.layers.0.mlp.down_proj'
    ]

    for layer_name in target_layers:
        print(f"\n📍 {layer_name}:")
        comparison = tracker.compare_layers("original", "quantized", layer_name)
        if comparison:
            print(f"  权重数据类型变化: {comparison['weight_dtype_change']}")
            print(f"  是否添加量化参数: {comparison['quantization_added']}")
            if comparison['new_quantization_params']:
                print(f"  新增量化参数:")
                for param_name, param_info in comparison['new_quantization_params'].items():
                    print(f"    {param_name}: {param_info}")

    # 3. SmoothQuant操作详情
    print(f"\n🔧 SmoothQuant操作详情 (共{len(tracker.smoothquant_operations)}个操作):")
    for i, op in enumerate(tracker.smoothquant_operations):
        print(f"\n  操作{i+1}: {op['operation_type']} @ {op['layer_name']}")
        if 'original_stats' in op['details']:
            orig = op['details']['original_stats']
            new = op['details']['new_stats']
            print(f"    变化前: mean={orig['mean']:.6f}, std={orig['std']:.6f}")
            print(f"    变化后: mean={new['mean']:.6f}, std={new['std']:.6f}")

    # 4. GPTQ操作详情
    print(f"\n🎯 GPTQ操作详情 (共{len(tracker.gptq_operations)}个操作):")
    for i, op in enumerate(tracker.gptq_operations):
        print(f"\n  操作{i+1}: {op['operation_type']} @ {op['layer_name']}")
        details = op['details']
        print(f"    量化损失: {details['loss']:.8f}")
        print(f"    原始权重: {details['original_stats']['dtype']} {details['original_stats']['shape']}")
        print(f"    量化权重: {details['quantized_stats']['dtype']} {details['quantized_stats']['shape']}")
        print(f"    缩放因子: {details['scale_stats']['dtype']} {details['scale_stats']['shape']}")
        print(f"    零点: {details['zero_point_stats']['dtype']} {details['zero_point_stats']['shape']}")

    # 5. 保存详细报告到文件
    save_report_to_file(tracker)

def save_report_to_file(tracker):
    """保存报告到文件"""
    report = {
        'model_states': tracker.layer_info,
        'smoothquant_operations': tracker.smoothquant_operations,
        'gptq_operations': tracker.gptq_operations,
        'summary': {
            'total_smoothquant_ops': len(tracker.smoothquant_operations),
            'total_gptq_ops': len(tracker.gptq_operations),
            'key_findings': []
        }
    }

    # 添加关键发现
    if 'original' in tracker.layer_info and 'quantized' in tracker.layer_info:
        original_params = tracker.layer_info['original']['total_params']
        quantized_params = tracker.layer_info['quantized']['total_params']
        report['summary']['key_findings'].append(f"参数数量变化: {original_params:,} -> {quantized_params:,}")

    # 保存JSON报告
    with open('layer_quantization_detailed_report.json', 'w') as f:
        # 处理不可序列化的对象
        def json_serializer(obj):
            if hasattr(obj, 'item'):  # torch tensor scalar
                return obj.item()
            elif torch.is_tensor(obj):
                return obj.tolist()
            elif hasattr(obj, '__dict__'):
                return str(obj)
            return str(obj)

        json.dump(report, f, indent=2, default=json_serializer)

    print(f"\n💾 详细报告已保存到: layer_quantization_detailed_report.json")

if __name__ == "__main__":
    main()
