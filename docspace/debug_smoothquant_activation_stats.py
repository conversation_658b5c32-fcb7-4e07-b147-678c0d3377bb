#!/usr/bin/env python3
"""
Debug脚本：验证SmoothQuant激活统计收集的具体过程
追踪hook函数的执行和激活统计的更新
"""

import torch
import copy
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset

class SmoothQuantActivationTracker:
    """SmoothQuant激活统计追踪器"""
    
    def __init__(self):
        self.activation_stats = {}
        self.hook_calls = []
        self.sample_count = 0
    
    def create_tracking_hook(self, layer_name):
        """创建追踪hook函数"""
        def hook_fn(module, inp, out):
            # 记录hook调用
            self.hook_calls.append({
                'layer_name': layer_name,
                'sample_idx': self.sample_count,
                'input_shape': inp[0].shape if isinstance(inp, tuple) else inp.shape,
                'output_shape': out[0].shape if isinstance(out, tuple) else out.shape
            })
            
            # 处理输出
            if isinstance(out, tuple):
                out = out[0]
            
            # 重塑为2D
            hidden_dim = out.shape[-1]
            out_2d = out.view(-1, hidden_dim)
            
            # 计算当前batch的min/max
            current_mins = torch.min(out_2d, dim=0)[0]  # [hidden_dim]
            current_maxes = torch.max(out_2d, dim=0)[0]  # [hidden_dim]
            
            print(f"🔍 Hook调用: {layer_name}")
            print(f"   样本索引: {self.sample_count}")
            print(f"   输出形状: {out.shape} -> 重塑为: {out_2d.shape}")
            print(f"   当前min范围: [{current_mins.min():.6f}, {current_mins.max():.6f}]")
            print(f"   当前max范围: [{current_maxes.min():.6f}, {current_maxes.max():.6f}]")
            
            # 更新统计信息
            if layer_name in self.activation_stats:
                # 更新现有统计
                old_mins = self.activation_stats[layer_name]['min_vals'].clone()
                old_maxes = self.activation_stats[layer_name]['max_vals'].clone()
                
                new_mins = torch.minimum(old_mins, current_mins)
                new_maxes = torch.maximum(old_maxes, current_maxes)
                
                self.activation_stats[layer_name]['min_vals'] = new_mins
                self.activation_stats[layer_name]['max_vals'] = new_maxes
                self.activation_stats[layer_name]['update_count'] += 1
                
                # 计算变化
                min_changed = not torch.equal(old_mins, new_mins)
                max_changed = not torch.equal(old_maxes, new_maxes)
                
                print(f"   统计更新: min变化={min_changed}, max变化={max_changed}")
                
            else:
                # 初始化统计
                self.activation_stats[layer_name] = {
                    'min_vals': current_mins.clone(),
                    'max_vals': current_maxes.clone(),
                    'update_count': 1,
                    'first_seen_sample': self.sample_count
                }
                print(f"   初始化统计信息")
            
            # 计算动态范围
            dynamic_range = self.activation_stats[layer_name]['max_vals'] - self.activation_stats[layer_name]['min_vals']
            print(f"   当前动态范围: [{dynamic_range.min():.6f}, {dynamic_range.max():.6f}]")
            print()
        
        return hook_fn
    
    def register_hooks(self, model):
        """注册hook到目标层"""
        hooks = []
        target_layers = [
            'model.layers.0.input_layernorm',
            'model.layers.0.post_attention_layernorm'
        ]
        
        for name, module in model.named_modules():
            if name in target_layers:
                hook = module.register_forward_hook(self.create_tracking_hook(name))
                hooks.append(hook)
                print(f"✅ 注册hook到: {name}")
        
        return hooks
    
    def simulate_calibration(self, model, dataloader, num_samples=8):
        """模拟校准过程"""
        print("🚀 开始模拟SmoothQuant激活统计收集")
        print("="*80)
        
        # 注册hooks
        hooks = self.register_hooks(model)
        
        # 模拟校准过程
        model.eval()
        with torch.no_grad():
            for i, batch in enumerate(dataloader):
                if i >= num_samples:
                    break
                
                print(f"\n📊 处理样本 {i+1}/{num_samples}")
                print("-" * 40)
                
                self.sample_count = i
                
                # 前向传播 (触发hooks)
                outputs = model(**batch)
                
                print(f"✅ 样本 {i+1} 处理完成")
        
        # 移除hooks
        for hook in hooks:
            hook.remove()
        
        print(f"\n🎉 校准完成，共处理 {num_samples} 个样本")
        
        return self.activation_stats
    
    def analyze_statistics(self):
        """分析收集的统计信息"""
        print("\n📈 激活统计分析")
        print("="*80)
        
        for layer_name, stats in self.activation_stats.items():
            print(f"\n🔍 {layer_name}:")
            print(f"   更新次数: {stats['update_count']}")
            print(f"   首次观察: 样本 {stats['first_seen_sample']}")
            
            min_vals = stats['min_vals']
            max_vals = stats['max_vals']
            dynamic_range = max_vals - min_vals
            
            print(f"   最小值统计: mean={min_vals.mean():.6f}, std={min_vals.std():.6f}")
            print(f"   最小值范围: [{min_vals.min():.6f}, {min_vals.max():.6f}]")
            
            print(f"   最大值统计: mean={max_vals.mean():.6f}, std={max_vals.std():.6f}")
            print(f"   最大值范围: [{max_vals.min():.6f}, {max_vals.max():.6f}]")
            
            print(f"   动态范围统计: mean={dynamic_range.mean():.6f}, std={dynamic_range.std():.6f}")
            print(f"   动态范围: [{dynamic_range.min():.6f}, {dynamic_range.max():.6f}]")
    
    def simulate_smooth_factor_calculation(self, model):
        """模拟平滑因子计算"""
        print("\n🧮 模拟平滑因子计算")
        print("="*80)
        
        # 模拟映射关系
        mappings = [
            {
                'smooth_layer': 'model.layers.0.input_layernorm',
                'balance_layers': ['model.layers.0.self_attn.q_proj', 
                                 'model.layers.0.self_attn.k_proj',
                                 'model.layers.0.self_attn.v_proj']
            },
            {
                'smooth_layer': 'model.layers.0.post_attention_layernorm',
                'balance_layers': ['model.layers.0.mlp.gate_proj',
                                 'model.layers.0.mlp.up_proj']
            }
        ]
        
        smoothing_strength = 0.8  # α参数
        
        for mapping in mappings:
            smooth_layer_name = mapping['smooth_layer']
            balance_layer_names = mapping['balance_layers']
            
            print(f"\n📍 处理映射: {smooth_layer_name}")
            
            # 获取激活动态范围
            if smooth_layer_name in self.activation_stats:
                stats = self.activation_stats[smooth_layer_name]
                activation_scales = stats['max_vals'] - stats['min_vals']
                
                print(f"   激活动态范围: shape={activation_scales.shape}")
                print(f"   激活范围: [{activation_scales.min():.6f}, {activation_scales.max():.6f}]")
                
                # 获取权重动态范围
                weight_scales = []
                for layer_name in balance_layer_names:
                    layer = None
                    for name, module in model.named_modules():
                        if name == layer_name:
                            layer = module
                            break
                    
                    if layer is not None:
                        weight_scale = layer.weight.abs().max(dim=0, keepdim=True)[0]
                        weight_scales.append(weight_scale)
                        print(f"   {layer_name}: 权重范围 [{weight_scale.min():.6f}, {weight_scale.max():.6f}]")
                
                if weight_scales:
                    # 合并权重缩放因子
                    combined_weight_scales = 2.0 * torch.cat(weight_scales, dim=0).max(dim=0)[0]
                    print(f"   合并权重范围: [{combined_weight_scales.min():.6f}, {combined_weight_scales.max():.6f}]")
                    
                    # 计算平滑因子
                    smooth_scales = activation_scales.pow(smoothing_strength) / combined_weight_scales.pow(1 - smoothing_strength)
                    smooth_scales = torch.where(combined_weight_scales > 0.0, smooth_scales, activation_scales)
                    
                    print(f"   平滑因子 (α={smoothing_strength}): [{smooth_scales.min():.6f}, {smooth_scales.max():.6f}]")
                    print(f"   平滑因子统计: mean={smooth_scales.mean():.6f}, std={smooth_scales.std():.6f}")

def main():
    """主函数"""
    print("🔍 SmoothQuant激活统计收集详细分析")
    print("="*80)
    
    # 加载模型
    MODEL_ID = "/home/<USER>/single_llama"
    model = AutoModelForCausalLM.from_pretrained(MODEL_ID, torch_dtype=torch.float16)
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    # 准备数据
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(8))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=128, truncation=True, add_special_tokens=False)
    
    ds = ds.map(preprocess).map(tokenize, remove_columns=ds.column_names)
    
    # 创建dataloader
    from torch.utils.data import DataLoader
    dataloader = DataLoader(ds, batch_size=1, shuffle=False)
    
    # 创建追踪器
    tracker = SmoothQuantActivationTracker()
    
    # 执行模拟校准
    activation_stats = tracker.simulate_calibration(model, dataloader, num_samples=8)
    
    # 分析统计信息
    tracker.analyze_statistics()
    
    # 模拟平滑因子计算
    tracker.simulate_smooth_factor_calculation(model)
    
    print(f"\n🎉 分析完成!")
    print("="*80)
    print("关键发现:")
    print("1. SmoothQuant通过hook函数收集LayerNorm输出的激活统计")
    print("2. 每个样本都会更新全局的min/max统计")
    print("3. 动态范围用于计算平滑因子，平衡激活和权重的量化难度")
    print("4. 平滑因子应用后，激活更容易量化，权重稍难量化但可用GPTQ处理")

if __name__ == "__main__":
    main()
