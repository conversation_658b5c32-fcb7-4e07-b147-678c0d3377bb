#!/usr/bin/env python3
"""
测试PyTorch 2.5.1版本下的SmoothQuant是否能正常工作
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor.transformers import oneshot
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier

def main():
    print("=== PyTorch 2.5.1版本下的SmoothQuant测试 ===")
    
    # 检查版本
    print(f"PyTorch版本: {torch.__version__}")
    
    try:
        import torchvision
        print(f"TorchVision版本: {torchvision.__version__}")
    except ImportError:
        print("TorchVision未安装")
    
    # 模型路径和设备配置
    MODEL_ID = "/home/<USER>/single_llama"
    
    print("加载模型和tokenizer...")
    model = AutoModelForCausalLM.from_pretrained(
        MODEL_ID, device_map="auto", torch_dtype="auto",
    )
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    # 检查并添加填充标记
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    print("✅ 模型加载成功")
    
    # 数据集加载和预处理（使用小数据集快速测试）
    NUM_CALIBRATION_SAMPLES = 32
    MAX_SEQUENCE_LENGTH = 512
    
    print("加载数据集...")
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(NUM_CALIBRATION_SAMPLES))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    ds = ds.map(preprocess)
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=MAX_SEQUENCE_LENGTH, truncation=True, add_special_tokens=False)
    
    ds = ds.map(tokenize, remove_columns=ds.column_names)
    
    print("✅ 数据集准备完成")
    
    # 配置量化算法 - 使用原始配置测试是否还有FX tracing错误
    print("测试原始SmoothQuant配置...")
    recipe = [
        SmoothQuantModifier(smoothing_strength=0.8),
        GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
    ]
    
    try:
        print("开始SmoothQuant + GPTQ量化...")
        oneshot(
            model=model,
            dataset=ds,
            recipe=recipe,
            max_seq_length=MAX_SEQUENCE_LENGTH,
            num_calibration_samples=NUM_CALIBRATION_SAMPLES,
        )
        
        # 保存压缩后的模型
        SAVE_DIR = f"{MODEL_ID.split('/')[-1]}-W8A8-SmoothQuant-Torch25"
        model.save_pretrained(SAVE_DIR, save_compressed=True)
        tokenizer.save_pretrained(SAVE_DIR)
        
        print(f"🎉 成功！PyTorch 2.5.1下SmoothQuant工作正常！")
        print(f"模型已保存到: {SAVE_DIR}")
        return True
        
    except Exception as e:
        print(f"❌ 量化失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 检查是否是FX tracing错误
        if "symbolically traced variables cannot be used as inputs to control flow" in str(e):
            print("🔍 确认这仍然是FX tracing错误")
            print("看起来降级PyTorch版本并没有解决问题")
        elif "torchvision::nms does not exist" in str(e):
            print("🔍 这是torchvision兼容性错误")
        else:
            print("🔍 这是其他类型的错误")
        
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ 结论: PyTorch 2.5.1版本下SmoothQuant可以正常工作")
        print("问题已通过降级PyTorch版本解决！")
    else:
        print("\n❌ 结论: 降级PyTorch版本未能解决问题")
        print("可能需要其他解决方案")
