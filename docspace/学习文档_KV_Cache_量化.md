
# 学习文档: KV Cache 量化

本文档详细分析vLLM中KV Cache量化的实现原理和相关代码。

---

## 1. 为什么需要KV Cache量化？

*   **显存瓶颈:** 在LLM推理中，KV Cache是主要的显存消耗者。对于长序列或大批次大小，KV Cache占用的显存甚至可能超过模型权重本身。
    *   **计算公式:** `显存 (GB) ≈ 2 * num_layers * hidden_size * num_heads * head_dim * num_tokens * bytes_per_element / 1024^3`
*   **带宽瓶颈:** 在每次生成新token时，整个KV Cache都需要从HBM（高带宽内存）加载到GPU的SRAM中进行计算。巨大的Cache尺寸会增加内存带宽的压力，影响推理速度。

**KV Cache量化**通过将KV Cache从FP16/BF16转换为低比特格式（如INT8），可以直接解决以上两个问题：

1.  **减少显存占用:** INT8格式的KV Cache大小直接减半。
2.  **降低内存带宽:** 加载的数据量减半，可以加快Attention计算。

---

## 2. vLLM 中的KV Cache量化实现

vLLM的KV Cache量化是一种**动态量化**方案，它在运行时对KV Cache进行量化和反量化。

**核心源码路径:** `vllm/model_executor/layers/quantization/kv_cache.py`

### 2.1. 核心组件

*   **`KVCacheScalingFactorBase`:**
    *   这是一个抽象基类，定义了KV Cache缩放因子的基本接口。
    *   缩放因子是实现量化的关键，它记录了原始浮点数和量化整数之间的映射关系。
    *   **公式:** `quantized_value = float_value / scale`
    *   **反量化:** `float_value = quantized_value * scale`

*   **`PerTensorKVCacheScalingFactor`:**
    *   **策略:** 为整个KV Cache张量（跨所有token和所有head）计算一个单一的缩放因子。
    *   **实现:**
        1.  在`forward`方法中，输入`kv_cache`张量。
        2.  计算张量中所有元素的绝对值的最大值 `amax`。
        3.  `scale = amax / 127.0`。这里的127是INT8能表示的最大正数值。
        4.  将计算出的`scale`存储起来。
    *   **优点:** 实现简单，计算开销小。
    *   **缺点:** 如果KV Cache中存在个别极端离群点，会导致`amax`非常大，从而使得大部分数值在量化后都挤在0附近，损失大量精度。

*   **`PerTokenKVCacheScalingFactor`:**
    *   **策略:** 为每个token（在所有head维度上）计算一个独立的缩放因子。
    *   **实现:**
        1.  在`forward`方法中，输入`kv_cache`张量，其形状通常是 `[num_tokens, num_heads, head_dim]`。
        2.  在`dim=1`和`dim=2`（head和head_dim维度）上计算`amax`，得到一个形状为 `[num_tokens, 1, 1]` 的张量。
        3.  `scales = amax / 127.0`。现在我们有了一个与token数相等的缩放因子向量。
    *   **优点:** 能够更好地适应不同token激活值的动态范围，精度损失通常比Per-Tensor要小。
    *   **缺点:** 计算和存储缩放因子的开销略大。

### 2.2. 工作流程

KV Cache的量化和反量化过程紧密集成在Attention计算中。

**核心源码路径:** `vllm/model_executor/models/llama.py` (以Llama为例), `vllm/attention/ops/paged_attention.py`

1.  **配置启用:**
    *   在启动vLLM引擎时，通过`EngineArgs`指定`kv_cache_dtype="int8"`来启用KV Cache量化。
    *   vLLM会根据配置，在`Attention`层中初始化相应的缩放因子计算模块（如`PerTokenKVCacheScalingFactor`）。
    *   **源码:** `vllm/model_executor/models/llama.py: LlamaAttention.__init__`
        ```python
        # Simplified from LlamaAttention.__init__
        if kv_cache_dtype == "int8":
            self.kv_scale_func = PerTokenKVCacheScalingFactor(...)
        ```

2.  **计算K和V向量:**
    *   在`LlamaAttention.forward`中，首先通过`qkv_proj`计算出`q`, `k`, `v`向量，它们此时是FP16或BF16格式。

3.  **计算缩放因子并量化:**
    *   在将`k`和`v`向量写入物理Cache之前，会先调用缩放因子计算模块。
    *   **源码:** `vllm/model_executor/models/llama.py: LlamaAttention.forward`
        ```python
        # Simplified from LlamaAttention.forward
        # k and v are in FP16/BF16
        
        # If quantization is enabled
        if self.kv_scale_func is not None:
            # 1. Compute scaling factors for k and v
            k_scale = self.kv_scale_func(k)
            v_scale = self.kv_scale_func(v)
            
            # 2. The computed scales are passed to the reshape_and_cache op
            # The quantization happens inside this CUDA op
            self.reshape_and_cache(k, v, k_cache, v_cache, ..., k_scale, v_scale)
        else:
            self.reshape_and_cache(k, v, k_cache, v_cache, ...)
        ```
    *   `reshape_and_cache`是一个CUDA操作，它接收FP16的`k`, `v`和FP16的`scales`，在Kernel内部完成`k_quantized = k / k_scale`的计算，并将INT8结果写入`k_cache`。

4.  **Attention计算 (反量化):**
    *   核心的`paged_attention` Kernel被调用。
    *   **源码:** `vllm/attention/ops/paged_attention.py: paged_attention_v1`
    *   这个Kernel接收INT8的`k_cache`, `v_cache`以及它们的`scales`。
    *   在Attention计算的内部，Kernel会首先进行**反量化**：
        *   `k_float = k_quantized * k_scale`
    *   然后，使用反量化后的FP16的`k`向量与FP16的`q`向量计算注意力分数。
    *   最后，用注意力分数和反量化后的FP16的`v`向量计算输出。

### 2.3. 流程图

```
+----------------------+
|  LlamaAttention.fwd  |
+----------+-----------+
           |
           v
+----------------------+
|  Compute Q, K, V     |  (FP16)
|  (from hidden_states)|
+----------+-----------+
           |
           v
+----------------------+
| kv_scale_func(K, V)  |  (Compute scales, FP16)
+----------+-----------+
           |
           v
+----------------------+
| reshape_and_cache    |  (CUDA Kernel)
| - Quantize K, V      |  (FP16 -> INT8)
| - Write to Cache     |
+----------+-----------+
           |
           v
+----------------------+
| paged_attention_v1   |  (CUDA Kernel)
| - Read Q (FP16)      |
| - Read K_cache (INT8)|
| - Read K_scales(FP16)|
| - Dequantize K       |  (INT8 -> FP16)
| - Compute Attention  |
| - ...                |
+----------------------+
           |
           v
+----------------------+
|   Output (FP16)      |
+----------------------+
```

---

## 3. 总结

*   vLLM的KV Cache量化是一个**在线（on-the-fly）**的过程，发生在每个前向传播步骤中。
*   它通过将FP16的KV值量化为INT8来节省显存和带宽，但在计算Attention分数之前，会在Kernel内部将其**反量化**回FP16。
*   这种设计的权衡是：
    *   **优点:** 无需修改模型架构或Attention计算的数学逻辑，易于实现和集成。
    *   **缺点:** 增加了量化和反量化的计算开销。但是，由于节省了大量的HBM读写时间，对于长序列或受内存带宽限制的场景，总体性能仍然可以得到提升。
*   支持**Per-Tensor**和**Per-Token**两种缩放策略，后者通常能提供更好的精度。
