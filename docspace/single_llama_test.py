from typing import List
from vllm.inputs import PromptType
import numpy as np
from vllm import LLM, SamplingParams
print("测试加载迷你模型...")
#模型路径
# model_path="/workspace/single_llama-W8A8-Dynamic-Per-Token-test"
model_path="/home/<USER>/single_llama"

# model_path="/root/code/models/deepseek_r1_mini/"
#指定本地路径并加载
llm = LLM(
    model=model_path,
    tokenizer=model_path,
    trust_remote_code=True,#必须启用以加载自定义模型
    enforce_eager=True,#关闭cudagraph
    max_model_len=25,#减小最大长度避免00M
    max_num_batched_tokens=25,#防止try run运行时间过长吧 default2048
    max_num_seqs=3,# default 256
    # swap_space=0.1,#CPU空间指定太大会报错
    #_enable_chunked_prefill=True
)

# prompts = [
#     "Hello, my name is",
#     "The president of the United States is",
#     "The capital of France is",
#     "The future of AI is",
# ]

np.random.seed(0)
dummy_prompt_token_ids = np.random.randint(50, size=(3, 10))# 50 vocabulary size
dummy_prompts: List[PromptType] = [{
    "prompt_token_ids": batch
} for batch in dummy_prompt_token_ids.tolist()]

sampling_params = SamplingParams(temperature=0, max_tokens=2,seed=0)
outputs = llm.generate(dummy_prompts ,sampling_params=sampling_params)
print(outputs)
print('inference done')