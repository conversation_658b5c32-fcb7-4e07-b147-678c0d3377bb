#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的 vLLM OpenAI 兼容接口压测脚本（单卡）
- 支持并发与重复次数配置
- 统计 RPS、P50/P95/P99 延迟、Token 吞吐等

使用示例：
  HOST=127.0.0.1 PORT=8000 python scripts/benchmark_single_llama.py \
    --model-name single_llama \
    --concurrency 8 \
    --requests 100 \
    --max-tokens 64 \
    --temperature 0

若服务使用其他 served model name，请通过 --model-name 指定。
"""

import os
import time
import argparse
import statistics
from typing import List

import requests

DEFAULT_HOST = os.environ.get("HOST", "127.0.0.1")
DEFAULT_PORT = int(os.environ.get("PORT", "8000"))


def parse_args():
    p = argparse.ArgumentParser()
    p.add_argument("--host", default=DEFAULT_HOST)
    p.add_argument("--port", type=int, default=DEFAULT_PORT)
    p.add_argument("--model-name", default=os.environ.get("SERVED_MODEL_NAME", "single_llama"))
    p.add_argument("--concurrency", type=int, default=4, help="并发请求数")
    p.add_argument("--requests", type=int, default=50, help="总请求数")
    p.add_argument("--prompt", default="用一句话介绍大模型压测是什么？")
    p.add_argument("--max-tokens", type=int, default=64)
    p.add_argument("--temperature", type=float, default=0.7)
    p.add_argument("--stream", action="store_true", help="是否开启流式返回")
    p.add_argument("--timeout", type=float, default=120.0)
    return p.parse_args()


def openai_chat(host: str, port: int, model: str, prompt: str, max_tokens: int, temperature: float, stream: bool, timeout: float):
    url = f"http://{host}:{port}/v1/chat/completions"
    headers = {"Content-Type": "application/json"}
    payload = {
        "model": model,
        "messages": [
            {"role": "system", "content": "你是一个有帮助的助手。"},
            {"role": "user", "content": prompt},
        ],
        "max_tokens": max_tokens,
        "temperature": temperature,
        "stream": stream,
    }
    if stream:
        with requests.post(url, json=payload, headers=headers, stream=True, timeout=timeout) as r:
            r.raise_for_status()
            text = ""
            start_ts = time.perf_counter()
            token_count = 0
            for line in r.iter_lines(decode_unicode=True):
                if not line or not line.startswith("data: "):
                    continue
                data = line[len("data: "):]
                if data == "[DONE]":
                    break
                # 简易解析（vLLM OpenAI 兼容 SSE）
                try:
                    import json
                    obj = json.loads(data)
                    delta = obj["choices"][0]["delta"].get("content", "")
                    text += delta
                    if delta:
                        token_count += 1
                except Exception:
                    pass
            latency = time.perf_counter() - start_ts
            return text, latency, token_count
    else:
        start_ts = time.perf_counter()
        r = requests.post(url, json=payload, headers=headers, timeout=timeout)
        r.raise_for_status()
        latency = time.perf_counter() - start_ts
        obj = r.json()
        text = obj["choices"][0]["message"]["content"]
        # 从 usage 中读 token 统计
        usage = obj.get("usage", {})
        token_count = usage.get("completion_tokens") or 0
        return text, latency, token_count


def run_benchmark(args):
    host, port = args.host, args.port
    model = args.model_name
    conc, total = args.concurrency, args.requests

    latencies: List[float] = []
    tokens: List[int] = []

    import concurrent.futures as cf

    def one_req(idx: int):
        try:
            _, lat, tok = openai_chat(host, port, model, args.prompt, args.max_tokens, args.temperature, args.stream, args.timeout)
            return lat, tok, None
        except Exception as e:
            return None, None, str(e)

    start_all = time.perf_counter()
    with cf.ThreadPoolExecutor(max_workers=conc) as ex:
        futures = [ex.submit(one_req, i) for i in range(total)]
        done_cnt = 0
        err_cnt = 0
        for f in cf.as_completed(futures):
            lat, tok, err = f.result()
            if err is None:
                latencies.append(lat)
                tokens.append(tok)
                done_cnt += 1
            else:
                err_cnt += 1
            if (done_cnt + err_cnt) % max(1, total // 10) == 0:
                print(f"progress: {done_cnt + err_cnt}/{total} (ok={done_cnt}, err={err_cnt})")

    dur = time.perf_counter() - start_all

    if not latencies:
        print("no successful requests, please check server logs")
        return

    latencies.sort()
    p50 = statistics.median(latencies)
    p95 = latencies[int(0.95 * (len(latencies) - 1))]
    p99 = latencies[int(0.99 * (len(latencies) - 1))]

    total_tokens = sum(tokens)
    rps = len(latencies) / dur
    tok_per_s = total_tokens / dur if dur > 0 else 0.0

    print("\n===== Benchmark Result =====")
    print(f"Requests: {len(latencies)}/{total} success")
    print(f"Duration: {dur:.3f}s  RPS: {rps:.2f}")
    print(f"Latency:  P50={p50:.3f}s  P95={p95:.3f}s  P99={p99:.3f}s")
    print(f"Throughput: tokens/s = {tok_per_s:.2f} (from completion tokens)")
    print("===========================\n")


if __name__ == "__main__":
    run_benchmark(parse_args())
