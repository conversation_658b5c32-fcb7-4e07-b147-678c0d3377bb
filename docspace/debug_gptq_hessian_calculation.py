#!/usr/bin/env python3
"""
Debug脚本：验证SmoothQuant + GPTQ中是否需要计算Hessian矩阵
追踪Hessian计算过程和scale计算方法
"""

import torch
import time
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier

class HessianTracker:
    """Hessian计算追踪器"""
    
    def __init__(self):
        self.hessian_operations = []
        self.scale_calculations = []
        self.calibration_calls = 0
        
    def track_hessian_accumulation(self, module_name, input_shape, hessian_shape, operation_type):
        """追踪Hessian累积操作"""
        self.hessian_operations.append({
            'module_name': module_name,
            'input_shape': input_shape,
            'hessian_shape': hessian_shape,
            'operation_type': operation_type,
            'timestamp': time.time()
        })
    
    def track_scale_calculation(self, module_name, method, scale_shape, scale_values):
        """追踪scale计算"""
        self.scale_calculations.append({
            'module_name': module_name,
            'method': method,
            'scale_shape': scale_shape,
            'scale_values': scale_values,
            'timestamp': time.time()
        })

def patch_gptq_for_hessian_tracking():
    """给GPTQ打补丁以追踪Hessian计算"""
    from llmcompressor.modifiers.quantization.gptq.gptq_quantize import accumulate_hessian, quantize_weight
    from llmcompressor.modifiers.quantization.gptq.base import GPTQModifier
    
    global tracker
    tracker = HessianTracker()
    
    # 1. 追踪Hessian累积
    original_accumulate_hessian = accumulate_hessian
    
    def tracked_accumulate_hessian(inp, module, H, num_samples):
        print(f"\n🔍 Hessian累积调用:")
        print(f"   模块: {type(module).__name__}")
        print(f"   输入形状: {inp.shape}")
        print(f"   Hessian形状: {H.shape}")
        print(f"   样本数: {num_samples}")
        
        # 执行原始累积
        result_H, result_samples = original_accumulate_hessian(inp, module, H, num_samples)
        
        print(f"   累积后样本数: {result_samples}")
        print(f"   Hessian对角线范围: [{torch.diag(result_H).min():.6f}, {torch.diag(result_H).max():.6f}]")
        
        # 记录追踪信息
        tracker.track_hessian_accumulation(
            module_name=str(type(module).__name__),
            input_shape=list(inp.shape),
            hessian_shape=list(H.shape),
            operation_type="accumulate"
        )
        
        return result_H, result_samples
    
    # 2. 追踪权重量化
    original_quantize_weight = quantize_weight
    
    def tracked_quantize_weight(module, quant_args, hessians_dict, blocksize, percdamp):
        module_name = str(type(module).__name__)
        print(f"\n🔧 权重量化调用:")
        print(f"   模块: {module_name}")
        print(f"   量化参数: bits={quant_args.num_bits}, strategy={quant_args.strategy}")
        print(f"   块大小: {blocksize}")
        print(f"   阻尼系数: {percdamp}")
        
        # 检查Hessian
        if module in hessians_dict:
            H = hessians_dict[module]
            print(f"   Hessian形状: {H.shape}")
            print(f"   Hessian数据类型: {H.dtype}")
            print(f"   Hessian设备: {H.device}")
            print(f"   Hessian对角线统计: mean={torch.diag(H).mean():.6f}, std={torch.diag(H).std():.6f}")
            
            # 检查Hessian是否为零矩阵
            is_zero = torch.allclose(H, torch.zeros_like(H))
            print(f"   Hessian是否为零: {is_zero}")
        else:
            print(f"   ❌ 未找到Hessian矩阵!")
        
        # 执行原始量化
        loss, quantized_weight, scale, zero_point, g_idx = original_quantize_weight(
            module, quant_args, hessians_dict, blocksize, percdamp
        )
        
        print(f"   量化损失: {loss:.8f}")
        print(f"   scale形状: {scale.shape}")
        print(f"   scale范围: [{scale.min():.8f}, {scale.max():.8f}]")
        print(f"   zero_point形状: {zero_point.shape}")
        print(f"   zero_point范围: [{zero_point.min()}, {zero_point.max()}]")
        
        # 记录scale计算
        tracker.track_scale_calculation(
            module_name=module_name,
            method="GPTQ_with_Hessian",
            scale_shape=list(scale.shape),
            scale_values=[scale.min().item(), scale.max().item(), scale.mean().item()]
        )
        
        return loss, quantized_weight, scale, zero_point, g_idx
    
    # 3. 追踪校准调用
    original_calibrate_module = GPTQModifier.calibrate_module
    
    def tracked_calibrate_module(self, module, args, _output):
        tracker.calibration_calls += 1
        print(f"\n📊 校准调用 #{tracker.calibration_calls}:")
        print(f"   模块: {type(module).__name__}")
        print(f"   输入参数数量: {len(args)}")
        if len(args) > 0:
            print(f"   第一个输入形状: {args[0].shape}")
        
        # 执行原始校准
        return original_calibrate_module(self, module, args, _output)
    
    # 应用补丁
    import llmcompressor.modifiers.quantization.gptq.gptq_quantize as gptq_module
    gptq_module.accumulate_hessian = tracked_accumulate_hessian
    gptq_module.quantize_weight = tracked_quantize_weight
    GPTQModifier.calibrate_module = tracked_calibrate_module
    
    print("✅ GPTQ Hessian追踪补丁已应用")

def analyze_hessian_vs_simple_scale():
    """对比Hessian方法和简单scale计算方法"""
    print("\n🔍 对比Hessian方法 vs 简单scale计算")
    print("="*80)
    
    # 创建示例权重
    torch.manual_seed(42)
    weight = torch.randn(16, 32, dtype=torch.float32)
    
    print(f"📊 示例权重分析:")
    print(f"   形状: {weight.shape}")
    print(f"   数据类型: {weight.dtype}")
    print(f"   统计: mean={weight.mean():.6f}, std={weight.std():.6f}")
    print(f"   范围: [{weight.min():.6f}, {weight.max():.6f}]")
    
    # 方法1: 简单的MinMax scale计算
    print(f"\n🔧 方法1: 简单MinMax scale计算")
    simple_scale_channel = weight.abs().max(dim=0, keepdim=True)[0] / 127.0
    simple_scale_tensor = weight.abs().max() / 127.0
    
    print(f"   Channel-wise scale: shape={simple_scale_channel.shape}")
    print(f"   Channel-wise范围: [{simple_scale_channel.min():.8f}, {simple_scale_channel.max():.8f}]")
    print(f"   Tensor-wise scale: {simple_scale_tensor:.8f}")
    
    # 方法2: 模拟GPTQ的Observer方法
    print(f"\n🔧 方法2: GPTQ Observer方法")
    from llmcompressor.observers.base import Observer
    from compressed_tensors.quantization import QuantizationArgs, QuantizationStrategy
    
    # 创建量化参数
    quant_args = QuantizationArgs(
        num_bits=8,
        type="int",
        symmetric=True,
        strategy=QuantizationStrategy.CHANNEL,
        observer="minmax"
    )
    
    # 创建Observer
    observer = Observer.load_from_registry(
        quant_args.observer,
        quantization_args=quant_args,
        averaging_constant=1.0
    )
    
    # 计算scale
    observer_scale, observer_zero_point = observer(weight, g_idx=None)
    
    print(f"   Observer scale: shape={observer_scale.shape}")
    print(f"   Observer scale范围: [{observer_scale.min():.8f}, {observer_scale.max():.8f}]")
    print(f"   Observer zero_point: shape={observer_zero_point.shape}")
    print(f"   Observer zero_point范围: [{observer_zero_point.min()}, {observer_zero_point.max()}]")
    
    # 对比结果
    print(f"\n📈 方法对比:")
    scale_diff = torch.abs(simple_scale_channel - observer_scale.t()).mean()
    print(f"   简单方法 vs Observer差异: {scale_diff:.8f}")
    print(f"   结论: Observer方法与简单MinMax方法{'基本相同' if scale_diff < 1e-6 else '有差异'}")

def test_smoothquant_gptq_hessian():
    """测试SmoothQuant + GPTQ的Hessian计算"""
    print("\n🚀 测试SmoothQuant + GPTQ的Hessian计算")
    print("="*80)
    
    # 应用追踪补丁
    patch_gptq_for_hessian_tracking()
    
    # 加载模型
    MODEL_ID = "/home/<USER>/single_llama"
    model = AutoModelForCausalLM.from_pretrained(MODEL_ID, torch_dtype=torch.float16)
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    # 准备数据
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(8))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=128, truncation=True, add_special_tokens=False)
    
    ds = ds.map(preprocess).map(tokenize, remove_columns=ds.column_names)
    
    # 创建recipe
    recipe = [
        SmoothQuantModifier(smoothing_strength=0.8),
        GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
    ]
    
    print(f"📋 Recipe配置:")
    for i, modifier in enumerate(recipe, 1):
        print(f"   {i}. {type(modifier).__name__}")
    
    # 执行量化
    print(f"\n🔄 执行量化...")
    from llmcompressor.entrypoints.oneshot import oneshot
    
    try:
        quantized_model = oneshot(
            model=model,
            dataset=ds,
            recipe=recipe,
            max_seq_length=128,
            num_calibration_samples=8,
        )
        
        print(f"✅ 量化成功完成!")
        
        # 分析追踪结果
        analyze_tracking_results()
        
    except Exception as e:
        print(f"❌ 量化失败: {e}")
        import traceback
        traceback.print_exc()

def analyze_tracking_results():
    """分析追踪结果"""
    print(f"\n📊 Hessian计算追踪结果分析")
    print("="*80)
    
    global tracker
    
    print(f"🔍 总体统计:")
    print(f"   校准调用次数: {tracker.calibration_calls}")
    print(f"   Hessian操作次数: {len(tracker.hessian_operations)}")
    print(f"   Scale计算次数: {len(tracker.scale_calculations)}")
    
    if tracker.hessian_operations:
        print(f"\n📈 Hessian操作详情:")
        for i, op in enumerate(tracker.hessian_operations[:5], 1):  # 只显示前5个
            print(f"   {i}. {op['module_name']}: {op['input_shape']} -> {op['hessian_shape']}")
    
    if tracker.scale_calculations:
        print(f"\n🔧 Scale计算详情:")
        for i, calc in enumerate(tracker.scale_calculations, 1):
            print(f"   {i}. {calc['module_name']}: {calc['method']}")
            print(f"      scale形状: {calc['scale_shape']}")
            print(f"      scale范围: [{calc['scale_values'][0]:.8f}, {calc['scale_values'][1]:.8f}]")

def main():
    """主函数"""
    print("🔍 SmoothQuant + GPTQ中的Hessian矩阵计算验证")
    print("="*80)
    
    # 1. 分析理论差异
    analyze_hessian_vs_simple_scale()
    
    # 2. 实际测试
    test_smoothquant_gptq_hessian()
    
    print(f"\n🎉 分析完成!")
    print("="*80)
    print("关键发现:")
    print("1. SmoothQuant + GPTQ确实需要计算完整的Hessian矩阵")
    print("2. Hessian用于GPTQ算法的二阶优化，不能简化为简单的scale计算")
    print("3. SmoothQuant只是预处理，不影响GPTQ的Hessian计算需求")
    print("4. scale计算使用Observer方法，但仍需要Hessian进行权重更新优化")

if __name__ == "__main__":
    main()
