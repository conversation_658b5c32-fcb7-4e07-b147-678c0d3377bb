# Single LLaMA Benchmark 故障排除指南

## 常见问题及解决方案

### 1. 服务启动问题

#### 问题: 服务无法启动
**症状**: 
- 启动脚本执行后无响应
- 出现CUDA相关错误
- 模型加载失败

**排查步骤**:
```bash
# 1. 检查GPU状态
nvidia-smi

# 2. 检查CUDA环境
echo $CUDA_VISIBLE_DEVICES

# 3. 检查模型文件
ls -la /workspace/single_llama-W8A8-Dynamic-Per-Token/

# 4. 检查端口占用
lsof -i :8000  # 如果有lsof命令
```

**解决方案**:
- 确保指定的GPU可用且有足够显存
- 验证模型文件完整性
- 更换端口或清理端口占用

#### 问题: 服务启动但无法访问
**症状**:
- 服务日志显示启动成功
- curl请求超时或连接拒绝

**解决方案**:
```bash
# 确保使用正确的HOST配置
HOST=127.0.0.1 ./scripts/start_single_llama_server.sh

# 或者在启动脚本中修改默认HOST
```

### 2. 推理请求问题

#### 问题: 上下文长度超限
**错误信息**:
```
ValueError: This model's maximum context length is 64 tokens. However, you requested X tokens
```

**解决方案**:
1. 减少输入prompt长度
2. 降低max_tokens参数
3. 移除不必要的system message

**示例修改**:
```python
# 原始请求（可能超限）
{
  "messages": [
    {"role": "system", "content": "你是一个有帮助的助手。"},
    {"role": "user", "content": "用一句话介绍大模型压测是什么？"}
  ],
  "max_tokens": 64
}

# 修改后请求（适合64 token限制）
{
  "messages": [
    {"role": "user", "content": "Hi"}
  ],
  "max_tokens": 8
}
```

#### 问题: 请求超时
**症状**:
- 请求长时间无响应
- 出现timeout错误

**排查步骤**:
```bash
# 1. 检查服务负载
curl -s http://127.0.0.1:8000/metrics | grep -i request

# 2. 检查GPU使用率
nvidia-smi

# 3. 查看服务日志
# 检查vLLM服务的终端输出
```

**解决方案**:
- 增加timeout参数
- 减少并发请求数
- 检查GPU内存是否充足

### 3. Benchmark测试问题

#### 问题: 所有请求都失败
**症状**:
```
progress: 20/20 (ok=0, err=20)
no successful requests, please check server logs
```

**排查步骤**:
1. 检查服务是否正常运行
2. 验证模型名称是否正确
3. 检查请求参数是否合理

**解决方案**:
```bash
# 1. 验证服务状态
curl -s http://127.0.0.1:8000/health

# 2. 检查模型列表
curl -s http://127.0.0.1:8000/v1/models

# 3. 手动测试单个请求
curl -X POST "http://127.0.0.1:8000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "single_llama",
    "messages": [{"role": "user", "content": "Hi"}],
    "max_tokens": 5,
    "temperature": 0.0
  }'
```

#### 问题: 性能指标异常
**症状**:
- RPS过低
- 延迟过高
- 吞吐量不符合预期

**排查步骤**:
```bash
# 1. 检查GPU利用率
nvidia-smi -l 1

# 2. 检查系统资源
top
free -h

# 3. 调整测试参数
# 减少并发数，观察单请求性能
python3 scripts/benchmark_single_llama.py --concurrency 1 --requests 10
```

### 4. 内存相关问题

#### 问题: GPU内存不足
**错误信息**:
```
CUDA out of memory
```

**解决方案**:
1. 减少MAX_MODEL_LEN
2. 降低MAX_BATCHED_TOKENS
3. 减少MAX_NUM_SEQS
4. 使用更小的GPU内存利用率

**配置示例**:
```bash
# 保守配置
MAX_MODEL_LEN=32 MAX_BATCHED_TOKENS=32 MAX_NUM_SEQS=4 GPU_MEM_UTIL=0.8 ./scripts/start_single_llama_server.sh
```

#### 问题: 系统内存不足
**症状**:
- 服务启动缓慢
- 系统响应迟缓

**解决方案**:
- 关闭不必要的进程
- 增加swap空间
- 使用更小的模型配置

### 5. 网络连接问题

#### 问题: 连接被拒绝
**错误信息**:
```
Connection refused
curl: (7) Failed to connect to 127.0.0.1 port 8000
```

**排查步骤**:
```bash
# 1. 检查端口监听
netstat -tlnp | grep 8000  # 如果有netstat
ss -tlnp | grep 8000       # 如果有ss

# 2. 检查防火墙设置
# 3. 验证服务进程状态
ps aux | grep vllm
```

### 6. 性能优化建议

#### 提升吞吐量
1. 增加并发数（在GPU内存允许范围内）
2. 调整MAX_BATCHED_TOKENS
3. 优化MAX_NUM_SEQS设置
4. 使用流式输出减少延迟

#### 降低延迟
1. 启用CUDA Graph（移除--enforce-eager）
2. 使用流式输出
3. 减少batch size
4. 优化prompt长度

#### 配置示例
```bash
# 高吞吐量配置
MAX_BATCHED_TOKENS=128 MAX_NUM_SEQS=16 ./scripts/start_single_llama_server.sh

# 低延迟配置  
MAX_BATCHED_TOKENS=32 MAX_NUM_SEQS=4 ./scripts/start_single_llama_server.sh
```

## 调试工具和命令

### 服务状态检查
```bash
# 健康检查
curl -s http://127.0.0.1:8000/health

# 模型信息
curl -s http://127.0.0.1:8000/v1/models | jq

# 服务指标
curl -s http://127.0.0.1:8000/metrics
```

### 系统监控
```bash
# GPU监控
nvidia-smi -l 1

# 系统资源
htop
iostat 1

# 网络连接
netstat -an | grep 8000
```

### 日志分析
```bash
# 查看vLLM服务日志
# 直接查看启动服务的终端输出

# 系统日志
dmesg | tail
journalctl -f
```

## 联系支持

如果遇到本指南未涵盖的问题，请：

1. 收集相关日志和错误信息
2. 记录复现步骤
3. 提供系统环境信息
4. 包含具体的配置参数

这将有助于快速定位和解决问题。
