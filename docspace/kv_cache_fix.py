#!/usr/bin/env python3
"""
专门解决 vLLM KV 缓存内存不足问题的脚本
针对错误: "No available memory for the cache blocks"
"""

import os
import torch
import gc
from vllm import LLM, SamplingParams

def force_cleanup():
    """强制清理所有GPU内存"""
    print("=== 强制清理GPU内存 ===")
    
    # 清理PyTorch缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
        
        # 强制垃圾回收
        gc.collect()
        
        # 显示清理后的内存状态
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1024**3
            reserved = torch.cuda.memory_reserved(i) / 1024**3
            print(f"GPU {i} - 已分配: {allocated:.2f} GB, 已保留: {reserved:.2f} GB")

def load_model_minimal_memory(model_name):
    """使用最小内存配置加载模型"""
    
    # 极端保守的配置序列
    configs = [
        # 配置1: 非常小的序列长度
        {
            "name": "超小序列长度",
            "max_model_len": 32,
            "gpu_memory_utilization": 0.5,
            "enable_prefix_caching": False,
            "block_size": 8,  # 减小块大小
        },
        # 配置2: 最小可能配置
        {
            "name": "最小配置",
            "max_model_len": 16,
            "gpu_memory_utilization": 0.4,
            "enable_prefix_caching": False,
            "block_size": 4,
        },
        # 配置3: 极限配置
        {
            "name": "极限配置",
            "max_model_len": 8,
            "gpu_memory_utilization": 0.3,
            "enable_prefix_caching": False,
            "block_size": 2,
        }
    ]
    
    for config in configs:
        print(f"\n=== 尝试{config['name']} ===")
        print(f"max_model_len: {config['max_model_len']}")
        print(f"gpu_memory_utilization: {config['gpu_memory_utilization']}")
        print(f"block_size: {config['block_size']}")
        
        try:
            # 每次尝试前都强制清理内存
            force_cleanup()
            
            # 尝试加载模型
            llm = LLM(
                model=model_name,
                tensor_parallel_size=1,
                max_model_len=config["max_model_len"],
                enable_prefix_caching=config["enable_prefix_caching"],
                trust_remote_code=True,
                gpu_memory_utilization=config["gpu_memory_utilization"],
                block_size=config["block_size"],
                # 添加更多内存优化选项
                enforce_eager=True,  # 禁用CUDA图以节省内存
                disable_custom_all_reduce=True,  # 禁用自定义all-reduce
            )
            
            print(f"✅ {config['name']}加载成功！")
            return llm
            
        except Exception as e:
            print(f"❌ {config['name']}失败:")
            print(f"   错误: {str(e)[:200]}...")
            
            # 强制清理后继续
            force_cleanup()
            continue
    
    return None

def test_inference(llm):
    """测试推理功能"""
    print("\n=== 测试推理 ===")
    
    # 使用非常短的提示
    prompts = ["Hi"]
    sampling_params = SamplingParams(
        temperature=0.0,
        top_p=1.0,
        max_tokens=5  # 只生成5个token
    )
    
    try:
        outputs = llm.generate(prompts, sampling_params)
        for output in outputs:
            print(f"输入: {output.prompt}")
            print(f"输出: {output.outputs[0].text}")
        return True
    except Exception as e:
        print(f"推理失败: {e}")
        return False

def main():
    """主函数"""
    print("KV缓存内存问题修复工具")
    print("=" * 50)
    
    # 模型路径
    model_name = "/home/<USER>/Qwen-QwQ-32B-W8A8-SmoothQuant"
    
    # 初始内存清理
    force_cleanup()
    
    # 尝试加载模型
    print("开始尝试加载模型...")
    llm = load_model_minimal_memory(model_name)
    
    if llm is None:
        print("\n❌ 所有配置都失败了")
        print("\n建议:")
        print("1. 检查是否有其他进程占用GPU内存")
        print("2. 尝试重启Python进程")
        print("3. 考虑使用更小的模型")
        print("4. 增加GPU内存或使用CPU offloading")
        return False
    
    # 测试推理
    success = test_inference(llm)
    
    if success:
        print("\n✅ 模型加载和推理都成功！")
        print("现在可以使用这个配置进行进一步的开发和调试")
    else:
        print("\n⚠️ 模型加载成功但推理失败")
    
    # 最终内存状态
    print("\n=== 最终GPU内存状态 ===")
    force_cleanup()
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
