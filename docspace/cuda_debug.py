#!/usr/bin/env python3
"""
CUDA Device-Side Assert 调试工具
专门用于调试 vLLM 中的 CUDA device-side assert triggered 错误
"""

import os
import torch
import logging
import subprocess
import sys
from vllm import LLM, SamplingParams

# 设置CUDA调试环境变量
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'  # 同步CUDA调用
os.environ['TORCH_USE_CUDA_DSA'] = '1'    # 启用设备端断言
os.environ['NCCL_DEBUG'] = 'INFO'         # NCCL调试信息
os.environ['VLLM_LOGGING_LEVEL'] = 'DEBUG'  # vLLM详细日志

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_cuda_compute_sanitizer():
    """检查是否可以使用CUDA compute-sanitizer"""
    try:
        result = subprocess.run(['compute-sanitizer', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ CUDA compute-sanitizer 可用")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ CUDA compute-sanitizer 不可用")
    print("建议安装: sudo apt-get install cuda-sanitizer-tools")
    return False

def check_nvidia_smi():
    """检查nvidia-smi状态"""
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ nvidia-smi 正常")
            print(result.stdout)
            return True
    except FileNotFoundError:
        pass
    
    print("❌ nvidia-smi 不可用")
    return False

def check_cuda_environment():
    """全面检查CUDA环境"""
    print("=== CUDA环境全面检查 ===")
    
    # 基础CUDA检查
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return False
    
    print(f"✅ CUDA版本: {torch.version.cuda}")
    print(f"✅ PyTorch版本: {torch.__version__}")
    print(f"✅ 可用GPU数量: {torch.cuda.device_count()}")
    
    # 检查每个GPU
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        total_memory = props.total_memory / 1024**3
        allocated = torch.cuda.memory_allocated(i) / 1024**3
        
        print(f"\nGPU {i}: {props.name}")
        print(f"  总内存: {total_memory:.2f} GB")
        print(f"  已分配: {allocated:.2f} GB")
        print(f"  计算能力: {props.major}.{props.minor}")
        print(f"  多处理器数量: {props.multi_processor_count}")
        print(f"  最大线程数/块: {props.max_threads_per_block}")
        print(f"  最大块维度: {props.max_block_dim}")
        print(f"  最大网格维度: {props.max_grid_dim}")
    
    # 检查CUDA工具
    check_nvidia_smi()
    check_cuda_compute_sanitizer()
    
    return True

def test_basic_cuda_operations():
    """测试基础CUDA操作"""
    print("\n=== 测试基础CUDA操作 ===")
    
    try:
        # 测试基础张量操作
        print("测试基础张量创建...")
        x = torch.randn(100, 100, device='cuda')
        y = torch.randn(100, 100, device='cuda')
        
        print("测试矩阵乘法...")
        z = torch.matmul(x, y)
        
        print("测试内存拷贝...")
        z_cpu = z.cpu()
        
        print("✅ 基础CUDA操作正常")
        return True
        
    except Exception as e:
        print(f"❌ 基础CUDA操作失败: {e}")
        return False

def test_flash_attention():
    """测试Flash Attention相关操作"""
    print("\n=== 测试Flash Attention ===")
    
    try:
        # 检查是否有flash_attn
        try:
            import flash_attn
            print(f"✅ flash_attn版本: {flash_attn.__version__}")
        except ImportError:
            print("❌ flash_attn未安装")
            return False
        
        # 测试简单的attention计算
        batch_size, seq_len, head_dim = 1, 32, 64
        num_heads = 8
        
        q = torch.randn(batch_size, seq_len, num_heads, head_dim, 
                       device='cuda', dtype=torch.float16)
        k = torch.randn(batch_size, seq_len, num_heads, head_dim, 
                       device='cuda', dtype=torch.float16)
        v = torch.randn(batch_size, seq_len, num_heads, head_dim, 
                       device='cuda', dtype=torch.float16)
        
        print("测试flash attention...")
        from flash_attn import flash_attn_func
        out = flash_attn_func(q, k, v)
        
        print("✅ Flash Attention测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Flash Attention测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_vllm_minimal():
    """使用最小配置调试vLLM"""
    print("\n=== vLLM最小配置调试 ===")
    
    model_name = "/home/<USER>/single_llama"
    
    # 极简配置
    config = {
        "tensor_parallel_size": 1,
        "gpu_memory_utilization": 0.3,
        "max_model_len": 16,  # 极小序列长度
        "enable_prefix_caching": False,
        "trust_remote_code": True,
        "block_size": 4,
        "enforce_eager": True,
        "disable_custom_all_reduce": True,
        "max_num_seqs": 1,  # 只处理一个序列
    }
    
    print("配置参数:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    try:
        print("\n开始加载模型...")
        torch.cuda.empty_cache()
        
        llm = LLM(model=model_name, **config)
        print("✅ 模型加载成功！")
        
        # 极简推理测试
        print("\n开始推理测试...")
        prompts = ["Hi"]
        sampling_params = SamplingParams(
            temperature=0.0,
            max_tokens=1,  # 只生成1个token
            use_beam_search=False
        )
        
        outputs = llm.generate(prompts, sampling_params)
        print("✅ 推理成功！")
        
        for output in outputs:
            print(f"输入: {output.prompt}")
            print(f"输出: {output.outputs[0].text}")
        
        return True
        
    except Exception as e:
        print(f"❌ vLLM调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主调试流程"""
    print("CUDA Device-Side Assert 调试工具")
    print("=" * 60)
    
    # 步骤1: 检查CUDA环境
    if not check_cuda_environment():
        print("❌ CUDA环境检查失败，无法继续")
        return False
    
    # 步骤2: 测试基础CUDA操作
    if not test_basic_cuda_operations():
        print("❌ 基础CUDA操作失败，请检查CUDA安装")
        return False
    
    # 步骤3: 测试Flash Attention
    if not test_flash_attention():
        print("⚠️ Flash Attention测试失败，可能是错误原因")
    
    # 步骤4: 调试vLLM
    if debug_vllm_minimal():
        print("\n🎉 vLLM调试成功！")
        return True
    else:
        print("\n❌ vLLM调试失败")
        print("\n建议:")
        print("1. 检查模型文件是否完整")
        print("2. 尝试更小的max_model_len")
        print("3. 检查GPU内存是否足够")
        print("4. 尝试使用compute-sanitizer运行:")
        print("   compute-sanitizer python cuda_debug.py")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
