#!/usr/bin/env bash
set -euo pipefail

# Single LLaMA Benchmark 快速测试脚本
# 用于快速验证single_llama模型的benchmark性能

echo "=== Single LLaMA Benchmark 快速测试 ==="
echo "开始时间: $(date)"
echo

# 配置参数
HOST=${HOST:-127.0.0.1}
PORT=${PORT:-8000}
MODEL_NAME=${MODEL_NAME:-single_llama}
GPU_ID=${GPU_ID:-3}

echo "配置参数:"
echo "  HOST: $HOST"
echo "  PORT: $PORT"
echo "  MODEL_NAME: $MODEL_NAME"
echo "  GPU_ID: $GPU_ID"
echo

# 检查服务是否运行
echo "1. 检查服务状态..."
if curl -s -f "http://$HOST:$PORT/health" > /dev/null; then
    echo "✅ 服务正在运行"
else
    echo "❌ 服务未运行，请先启动vLLM服务"
    echo "启动命令示例:"
    echo "CUDA_VISIBLE_DEVICES=$GPU_ID HOST=$HOST ./scripts/start_single_llama_server.sh"
    exit 1
fi

# 检查模型是否可用
echo
echo "2. 检查模型可用性..."
if curl -s "http://$HOST:$PORT/v1/models" | grep -q "$MODEL_NAME"; then
    echo "✅ 模型 $MODEL_NAME 可用"
else
    echo "❌ 模型 $MODEL_NAME 不可用"
    exit 1
fi

# 基础功能测试
echo
echo "3. 基础功能测试..."
RESPONSE=$(curl -s -X POST "http://$HOST:$PORT/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "'$MODEL_NAME'",
    "messages": [{"role": "user", "content": "Hi"}],
    "max_tokens": 5,
    "temperature": 0.0
  }')

if echo "$RESPONSE" | grep -q "choices"; then
    echo "✅ 基础推理功能正常"
else
    echo "❌ 基础推理功能异常"
    echo "响应: $RESPONSE"
    exit 1
fi

# Benchmark测试
echo
echo "4. 运行Benchmark测试..."

echo
echo "4.1 小规模测试 (4并发, 20请求)..."
python3 scripts/benchmark_single_llama.py \
  --host "$HOST" \
  --port "$PORT" \
  --model-name "$MODEL_NAME" \
  --concurrency 4 \
  --requests 20 \
  --max-tokens 8 \
  --temperature 0.0

echo
echo "4.2 中等规模测试 (8并发, 50请求)..."
python3 scripts/benchmark_single_llama.py \
  --host "$HOST" \
  --port "$PORT" \
  --model-name "$MODEL_NAME" \
  --concurrency 8 \
  --requests 50 \
  --max-tokens 8 \
  --temperature 0.0

echo
echo "4.3 流式输出测试 (4并发, 20请求)..."
python3 scripts/benchmark_single_llama.py \
  --host "$HOST" \
  --port "$PORT" \
  --model-name "$MODEL_NAME" \
  --concurrency 4 \
  --requests 20 \
  --max-tokens 8 \
  --temperature 0.0 \
  --stream

echo
echo "=== 测试完成 ==="
echo "结束时间: $(date)"
echo
echo "如需运行更大规模测试，请手动执行:"
echo "python3 scripts/benchmark_single_llama.py --host $HOST --port $PORT --model-name $MODEL_NAME --concurrency 16 --requests 200 --max-tokens 8"
