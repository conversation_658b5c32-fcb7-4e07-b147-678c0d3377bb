#!/usr/bin/env python3
"""
逐步量化教程 - 详细展示每个步骤的代码运行流程
包含完整的参数配置、调试信息和最佳实践
"""

import torch
import time
import json
import os
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor import oneshot
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier
import logging

# 配置日志以查看详细过程
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuantizationTutorial:
    """逐步量化教程类"""
    
    def __init__(self):
        self.config = {}
        self.metrics = {}
        self.debug_info = {}
        
    def step1_environment_setup(self):
        """步骤1: 环境设置与验证"""
        print("=" * 80)
        print("步骤1: 环境设置与验证")
        print("=" * 80)
        
        # 检查PyTorch版本
        torch_version = torch.__version__
        print(f"🔍 PyTorch版本: {torch_version}")
        
        if not torch_version.startswith('2.5'):
            print("⚠️  警告: 建议使用PyTorch 2.5.1以避免FX tracing问题")
        
        # 检查CUDA
        cuda_available = torch.cuda.is_available()
        print(f"🔍 CUDA可用: {cuda_available}")
        
        if cuda_available:
            gpu_count = torch.cuda.device_count()
            print(f"🔍 GPU数量: {gpu_count}")
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                print(f"   GPU {i}: {gpu_name}")
        
        # 验证关键导入
        try:
            from llmcompressor import oneshot
            from llmcompressor.modifiers.quantization import GPTQModifier
            from llmcompressor.modifiers.smoothquant import SmoothQuantModifier
            print("✅ 所有依赖导入成功")
        except ImportError as e:
            print(f"❌ 导入失败: {e}")
            raise
        
        self.debug_info['environment'] = {
            'torch_version': torch_version,
            'cuda_available': cuda_available,
            'gpu_count': torch.cuda.device_count() if cuda_available else 0
        }
        
        return True
    
    def step2_model_loading(self, model_path="/home/<USER>/single_llama"):
        """步骤2: 模型加载与配置"""
        print("\n" + "=" * 80)
        print("步骤2: 模型加载与配置")
        print("=" * 80)
        
        print(f"📥 加载模型: {model_path}")
        
        # 模型加载配置详解
        model_config = {
            "device_map": "auto",        # 自动分配设备
            "torch_dtype": "auto",       # 自动选择数据类型
            "trust_remote_code": True,   # 允许自定义代码
            "low_cpu_mem_usage": True,   # 降低CPU内存使用
        }
        
        print(f"🔧 模型加载配置: {model_config}")
        
        start_time = time.time()
        
        # 加载模型
        model = AutoModelForCausalLM.from_pretrained(model_path, **model_config)
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        load_time = time.time() - start_time
        
        # 模型信息分析
        print(f"✅ 模型加载完成 ({load_time:.2f}s)")
        print(f"📊 模型类型: {type(model).__name__}")
        print(f"📊 模型配置: {model.config}")
        
        # 参数统计
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"📊 总参数数: {total_params:,}")
        print(f"📊 可训练参数: {trainable_params:,}")
        print(f"📊 模型大小: {total_params * 2 / 1024**2:.2f}MB (FP16)")
        
        # 分析模型结构
        print(f"\n🏗️ 模型结构分析:")
        layer_count = {}
        linear_layers = []
        
        for name, module in model.named_modules():
            module_type = type(module).__name__
            layer_count[module_type] = layer_count.get(module_type, 0) + 1
            
            if isinstance(module, torch.nn.Linear):
                linear_layers.append({
                    'name': name,
                    'in_features': module.in_features,
                    'out_features': module.out_features,
                    'bias': module.bias is not None
                })
        
        print(f"📋 层类型统计:")
        for layer_type, count in sorted(layer_count.items()):
            print(f"   {layer_type}: {count}个")
        
        print(f"\n🎯 Linear层详情 (量化目标):")
        for layer_info in linear_layers:
            print(f"   {layer_info['name']}: [{layer_info['in_features']} → {layer_info['out_features']}]")
        
        # 处理tokenizer
        original_vocab_size = len(tokenizer)
        if tokenizer.pad_token is None:
            tokenizer.add_special_tokens({'pad_token': '[PAD]'})
            model.resize_token_embeddings(len(tokenizer))
            print(f"🔧 添加pad_token: {original_vocab_size} → {len(tokenizer)}")
        
        print(f"📝 Tokenizer信息:")
        print(f"   词汇表大小: {len(tokenizer)}")
        print(f"   特殊token: {tokenizer.special_tokens_map}")
        
        # 保存配置信息
        self.config['model'] = {
            'path': model_path,
            'type': type(model).__name__,
            'total_params': total_params,
            'vocab_size': len(tokenizer),
            'linear_layers': linear_layers
        }
        
        self.metrics['load_time'] = load_time
        
        return model, tokenizer
    
    def step3_data_preparation(self, tokenizer, num_samples=32, max_length=512):
        """步骤3: 数据准备与校准数据集构建"""
        print("\n" + "=" * 80)
        print("步骤3: 数据准备与校准数据集构建")
        print("=" * 80)
        
        # 数据配置
        data_config = {
            'num_calibration_samples': num_samples,
            'max_sequence_length': max_length,
            'dataset_path': "/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/",
            'split': 'train_sft'
        }
        
        print(f"📊 数据配置: {json.dumps(data_config, indent=2)}")
        
        # 加载数据集
        print(f"📥 加载数据集: {data_config['dataset_path']}")
        ds = load_dataset(data_config['dataset_path'], split=data_config['split'])
        
        print(f"📊 原始数据集信息:")
        print(f"   总样本数: {len(ds):,}")
        print(f"   列名: {ds.column_names}")
        print(f"   特征: {ds.features}")
        
        # 查看原始数据样本
        sample = ds[0]
        print(f"\n📝 原始数据样本:")
        print(f"   消息数量: {len(sample['messages'])}")
        for i, msg in enumerate(sample['messages'][:2]):
            print(f"   消息{i+1}: {msg}")
        
        # 选择校准样本
        print(f"\n🎯 选择校准样本: {num_samples}个")
        ds = ds.shuffle(seed=42).select(range(min(num_samples, len(ds))))
        
        # 预处理: 应用chat template
        print(f"🔄 应用chat template...")
        
        def preprocess(example):
            """预处理函数详解"""
            text = tokenizer.apply_chat_template(
                example["messages"], 
                tokenize=False,
                add_generation_prompt=False
            )
            return {"text": text}
        
        ds = ds.map(preprocess)
        
        # 查看预处理结果
        processed_sample = ds[0]['text']
        print(f"📝 预处理后样本:")
        print(f"   文本长度: {len(processed_sample)} 字符")
        print(f"   文本预览: {processed_sample[:200]}...")
        
        # Tokenization
        print(f"🔤 执行tokenization...")
        
        def tokenize(sample):
            """Tokenization函数详解"""
            return tokenizer(
                sample["text"],
                padding=True,              # 填充到相同长度
                max_length=max_length,     # 最大长度限制
                truncation=True,           # 截断超长序列
                add_special_tokens=False,  # 不添加额外特殊token
                return_tensors=None        # 返回Python list
            )
        
        ds = ds.map(tokenize, remove_columns=ds.column_names)
        
        # 分析tokenization结果
        print(f"📊 Tokenization结果分析:")
        token_sample = ds[0]
        print(f"   input_ids长度: {len(token_sample['input_ids'])}")
        print(f"   attention_mask长度: {len(token_sample['attention_mask'])}")
        print(f"   input_ids样本: {token_sample['input_ids'][:20]}...")
        
        # 统计token长度分布
        token_lengths = [len(sample['input_ids']) for sample in ds]
        print(f"\n📈 Token长度统计:")
        print(f"   平均长度: {sum(token_lengths)/len(token_lengths):.1f}")
        print(f"   最小长度: {min(token_lengths)}")
        print(f"   最大长度: {max(token_lengths)}")
        print(f"   中位数: {sorted(token_lengths)[len(token_lengths)//2]}")
        
        # 保存数据配置
        self.config['data'] = data_config
        self.config['data']['token_stats'] = {
            'avg_length': sum(token_lengths)/len(token_lengths),
            'min_length': min(token_lengths),
            'max_length': max(token_lengths)
        }
        
        return ds
    
    def step4_quantization_config(self, smoothing_strength=0.8, scheme="W8A8"):
        """步骤4: 量化配置详解"""
        print("\n" + "=" * 80)
        print("步骤4: 量化配置详解")
        print("=" * 80)
        
        # SmoothQuant配置详解
        print(f"🔧 SmoothQuant配置:")
        smoothquant_config = SmoothQuantModifier(
            smoothing_strength=smoothing_strength,  # 平滑强度 [0.0-1.0]
            mappings=None,                          # 自动推断映射关系
            num_calibration_steps=None,             # 使用全部校准数据
        )
        
        print(f"   平滑强度: {smoothquant_config.smoothing_strength}")
        print(f"   映射关系: {smoothquant_config.mappings} (自动推断)")
        print(f"   校准步数: {smoothquant_config.num_calibration_steps} (全部数据)")
        
        # GPTQ配置详解
        print(f"\n🔧 GPTQ配置:")
        gptq_config = GPTQModifier(
            targets="Linear",           # 目标层类型
            scheme=scheme,              # 量化方案
            ignore=["lm_head"],        # 忽略的层
            dampening_frac=0.01,       # 阻尼系数
            block_size=128,            # 块大小
        )
        
        print(f"   目标层: {gptq_config.targets}")
        print(f"   量化方案: {gptq_config.scheme}")
        print(f"   忽略层: {gptq_config.ignore}")
        print(f"   阻尼系数: {gptq_config.dampening_frac}")
        print(f"   块大小: {gptq_config.block_size}")
        
        # 量化方案说明
        scheme_explanation = {
            "W8A8": "权重8位 + 激活8位 (最大压缩)",
            "W8A16": "权重8位 + 激活16位 (平衡方案)",
            "W4A16": "权重4位 + 激活16位 (极致压缩)"
        }
        print(f"\n📋 量化方案说明:")
        for s, desc in scheme_explanation.items():
            marker = "👉" if s == scheme else "  "
            print(f"   {marker} {s}: {desc}")
        
        # 创建量化recipe
        recipe = [smoothquant_config, gptq_config]
        
        print(f"\n📋 量化Recipe (执行顺序):")
        for i, modifier in enumerate(recipe, 1):
            print(f"   {i}. {type(modifier).__name__}")
        
        # 保存配置
        self.config['quantization'] = {
            'smoothing_strength': smoothing_strength,
            'scheme': scheme,
            'recipe_order': [type(m).__name__ for m in recipe]
        }
        
        return recipe
    
    def step5_quantization_execution(self, model, dataset, recipe, max_seq_length=512, num_calibration_samples=32):
        """步骤5: 量化执行与监控"""
        print("\n" + "=" * 80)
        print("步骤5: 量化执行与监控")
        print("=" * 80)
        
        # 量化参数
        quantization_params = {
            'max_seq_length': max_seq_length,
            'num_calibration_samples': num_calibration_samples,
            'output_dir': 'quantized_model_tutorial'
        }
        
        print(f"🚀 量化参数: {json.dumps(quantization_params, indent=2)}")
        
        # 设置详细日志
        logging.getLogger("llmcompressor").setLevel(logging.INFO)
        
        print(f"\n🔄 开始量化过程...")
        print(f"   这个过程包含两个主要阶段:")
        print(f"   1. SmoothQuant预处理 - 激活平滑")
        print(f"   2. GPTQ量化 - 权重压缩")
        
        start_time = time.time()
        
        try:
            # 执行量化
            oneshot(
                model=model,
                dataset=dataset,
                recipe=recipe,
                max_seq_length=max_seq_length,
                num_calibration_samples=num_calibration_samples,
                output_dir=quantization_params['output_dir']
            )
            
            quantization_time = time.time() - start_time
            
            print(f"\n✅ 量化完成! 耗时: {quantization_time:.2f}秒")
            
            # 保存量化指标
            self.metrics['quantization_time'] = quantization_time
            
        except Exception as e:
            print(f"❌ 量化失败: {e}")
            raise
        
        return model
    
    def step6_result_analysis(self, model, output_dir='quantized_model_tutorial'):
        """步骤6: 结果分析与验证"""
        print("\n" + "=" * 80)
        print("步骤6: 结果分析与验证")
        print("=" * 80)
        
        # 分析保存的文件
        print(f"📁 分析输出文件: {output_dir}")
        
        if os.path.exists(output_dir):
            total_size = 0
            file_info = []
            
            for file in os.listdir(output_dir):
                file_path = os.path.join(output_dir, file)
                file_size = os.path.getsize(file_path)
                total_size += file_size
                file_info.append({
                    'name': file,
                    'size_mb': file_size / 1024**2
                })
            
            print(f"📊 输出文件详情:")
            for info in file_info:
                print(f"   {info['name']}: {info['size_mb']:.2f}MB")
            
            print(f"📊 总大小: {total_size / 1024**2:.2f}MB")
            
            # 计算压缩比
            original_size = self.config['model']['total_params'] * 2 / 1024**2  # FP16
            compression_ratio = original_size / (total_size / 1024**2)
            
            print(f"📊 压缩效果:")
            print(f"   原始大小: {original_size:.2f}MB")
            print(f"   压缩后: {total_size / 1024**2:.2f}MB")
            print(f"   压缩比: {compression_ratio:.2f}x")
            
            self.metrics['compression_ratio'] = compression_ratio
        
        # 分析量化后的模型权重
        print(f"\n🔍 量化后模型分析:")
        quantized_params = 0
        for name, param in model.named_parameters():
            if 'weight_scale' in name or 'weight_zero_point' in name:
                quantized_params += 1
                print(f"   量化参数: {name} - {param.shape} ({param.dtype})")
        
        print(f"📊 新增量化参数: {quantized_params}个")
        
        return True
    
    def generate_tutorial_report(self):
        """生成教程报告"""
        print("\n" + "=" * 80)
        print("教程总结报告")
        print("=" * 80)
        
        report = {
            'tutorial_config': self.config,
            'performance_metrics': self.metrics,
            'debug_information': self.debug_info
        }
        
        # 保存报告
        with open('quantization_tutorial_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📄 教程报告已保存: quantization_tutorial_report.json")
        
        # 打印关键信息
        print(f"\n📋 关键配置总结:")
        print(f"   模型: {self.config.get('model', {}).get('type', 'N/A')}")
        print(f"   参数数: {self.config.get('model', {}).get('total_params', 'N/A'):,}")
        print(f"   量化方案: {self.config.get('quantization', {}).get('scheme', 'N/A')}")
        print(f"   校准样本: {self.config.get('data', {}).get('num_calibration_samples', 'N/A')}")
        
        print(f"\n📊 性能指标:")
        print(f"   加载时间: {self.metrics.get('load_time', 'N/A'):.2f}s")
        print(f"   量化时间: {self.metrics.get('quantization_time', 'N/A'):.2f}s")
        print(f"   压缩比: {self.metrics.get('compression_ratio', 'N/A'):.2f}x")
        
        return report

def main():
    """主函数 - 运行完整教程"""
    print("🎓 模型量化完整流程教程")
    print("从载入到校准到量化的详细步骤")
    
    # 创建教程实例
    tutorial = QuantizationTutorial()
    
    try:
        # 步骤1: 环境设置
        tutorial.step1_environment_setup()
        
        # 步骤2: 模型加载
        model, tokenizer = tutorial.step2_model_loading()
        
        # 步骤3: 数据准备
        dataset = tutorial.step3_data_preparation(tokenizer, num_samples=32, max_length=512)
        
        # 步骤4: 量化配置
        recipe = tutorial.step4_quantization_config(smoothing_strength=0.8, scheme="W8A8")
        
        # 步骤5: 量化执行
        quantized_model = tutorial.step5_quantization_execution(
            model, dataset, recipe, max_seq_length=512, num_calibration_samples=32
        )
        
        # 步骤6: 结果分析
        tutorial.step6_result_analysis(quantized_model)
        
        # 生成报告
        tutorial.generate_tutorial_report()
        
        print(f"\n🎉 教程完成! 你已经学会了完整的模型量化流程!")
        
    except Exception as e:
        print(f"❌ 教程执行失败: {e}")
        raise

if __name__ == "__main__":
    main()
