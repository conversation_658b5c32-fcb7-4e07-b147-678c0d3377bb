#!/usr/bin/env python3
"""
Debug脚本：验证GPTQ如何处理W8A8配置
追踪scheme解析、配置生成和权重量化过程
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier

class W8A8ConfigTracker:
    """W8A8配置追踪器"""
    
    def __init__(self):
        self.scheme_parsing = []
        self.config_applications = []
        self.gptq_operations = []
        
    def track_scheme_parsing(self, scheme_name, parsed_config):
        """追踪scheme解析过程"""
        self.scheme_parsing.append({
            'scheme_name': scheme_name,
            'parsed_config': parsed_config,
            'has_weights': parsed_config.get('weights') is not None,
            'has_activations': parsed_config.get('input_activations') is not None
        })
    
    def track_config_application(self, module_name, quantization_scheme):
        """追踪配置应用过程"""
        config_info = {
            'module_name': module_name,
            'has_quantization_scheme': quantization_scheme is not None,
            'weights_config': None,
            'activation_config': None
        }
        
        if quantization_scheme:
            if hasattr(quantization_scheme, 'weights') and quantization_scheme.weights:
                config_info['weights_config'] = {
                    'num_bits': quantization_scheme.weights.num_bits,
                    'dynamic': quantization_scheme.weights.dynamic,
                    'strategy': quantization_scheme.weights.strategy,
                    'observer': quantization_scheme.weights.observer,
                    'symmetric': quantization_scheme.weights.symmetric
                }
            
            if hasattr(quantization_scheme, 'input_activations') and quantization_scheme.input_activations:
                config_info['activation_config'] = {
                    'num_bits': quantization_scheme.input_activations.num_bits,
                    'dynamic': quantization_scheme.input_activations.dynamic,
                    'strategy': quantization_scheme.input_activations.strategy,
                    'observer': quantization_scheme.input_activations.observer,
                    'symmetric': quantization_scheme.input_activations.symmetric
                }
        
        self.config_applications.append(config_info)
        return config_info

def analyze_w8a8_scheme_parsing():
    """分析W8A8 scheme解析过程"""
    print("🔍 分析W8A8 Scheme解析过程")
    print("="*80)
    
    # 1. 直接测试scheme解析
    from compressed_tensors.quantization.quant_scheme import preset_name_to_scheme, PRESET_SCHEMES
    
    print(f"📋 可用的预设方案:")
    for scheme_name in PRESET_SCHEMES.keys():
        print(f"   - {scheme_name}")
    
    # 2. 解析W8A8方案
    print(f"\n🔧 解析W8A8方案:")
    try:
        w8a8_scheme = preset_name_to_scheme("W8A8", ["Linear"])
        
        print(f"   目标层: {w8a8_scheme.targets}")
        
        if w8a8_scheme.weights:
            print(f"   权重配置:")
            print(f"     bits: {w8a8_scheme.weights.num_bits}")
            print(f"     dynamic: {w8a8_scheme.weights.dynamic}")
            print(f"     strategy: {w8a8_scheme.weights.strategy}")
            print(f"     observer: {w8a8_scheme.weights.observer}")
            print(f"     symmetric: {w8a8_scheme.weights.symmetric}")
            print(f"     type: {w8a8_scheme.weights.type}")
        
        if w8a8_scheme.input_activations:
            print(f"   激活配置:")
            print(f"     bits: {w8a8_scheme.input_activations.num_bits}")
            print(f"     dynamic: {w8a8_scheme.input_activations.dynamic}")
            print(f"     strategy: {w8a8_scheme.input_activations.strategy}")
            print(f"     observer: {w8a8_scheme.input_activations.observer}")
            print(f"     symmetric: {w8a8_scheme.input_activations.symmetric}")
            print(f"     type: {w8a8_scheme.input_activations.type}")
        
        # 3. 对比其他方案
        print(f"\n📊 对比其他量化方案:")
        schemes_to_compare = ["W8A16", "W4A16", "FP8"]
        
        for scheme_name in schemes_to_compare:
            if scheme_name in PRESET_SCHEMES:
                scheme = preset_name_to_scheme(scheme_name, ["Linear"])
                
                weight_bits = scheme.weights.num_bits if scheme.weights else "N/A"
                activation_bits = scheme.input_activations.num_bits if scheme.input_activations else "N/A"
                
                print(f"   {scheme_name}: W{weight_bits}A{activation_bits}")
        
        return w8a8_scheme
        
    except Exception as e:
        print(f"   ❌ 解析失败: {e}")
        return None

def patch_gptq_for_config_tracking():
    """给GPTQ打补丁以追踪配置处理"""
    from compressed_tensors.quantization import QuantizationMixin
    from llmcompressor.modifiers.quantization.gptq.base import GPTQModifier
    
    global tracker
    tracker = W8A8ConfigTracker()
    
    # 1. 追踪配置初始化
    original_initialize = QuantizationMixin.initialize_quantization
    
    def tracked_initialize_quantization(self, model):
        print(f"\n🔧 QuantizationMixin.initialize_quantization 执行")
        
        # 执行原始初始化
        result = original_initialize(self, model)
        
        # 追踪配置应用
        print(f"\n📊 检查量化配置应用:")
        for name, module in model.named_modules():
            if hasattr(module, 'quantization_scheme'):
                config_info = tracker.track_config_application(name, module.quantization_scheme)
                
                print(f"\n🔍 模块: {name}")
                print(f"   类型: {type(module).__name__}")
                
                if config_info['weights_config']:
                    wc = config_info['weights_config']
                    print(f"   权重配置: {wc['num_bits']}位, dynamic={wc['dynamic']}, "
                          f"strategy={wc['strategy']}, observer={wc['observer']}")
                
                if config_info['activation_config']:
                    ac = config_info['activation_config']
                    print(f"   激活配置: {ac['num_bits']}位, dynamic={ac['dynamic']}, "
                          f"strategy={ac['strategy']}, observer={ac['observer']}")
        
        return result
    
    # 2. 追踪GPTQ权重量化
    original_compress = GPTQModifier.compress_modules
    
    def tracked_compress_modules(self):
        print(f"\n🔧 GPTQModifier.compress_modules 执行")
        print(f"   处理模块数量: {len(self._num_samples)}")
        
        for module in list(self._num_samples.keys()):
            name = self._module_names[module]
            print(f"\n📍 处理模块: {name}")
            
            # 检查量化配置
            if hasattr(module, 'quantization_scheme'):
                scheme = module.quantization_scheme
                print(f"   有量化配置: True")
                
                # 检查权重配置
                if hasattr(scheme, 'weights') and scheme.weights:
                    print(f"   权重配置: {scheme.weights.num_bits}位, "
                          f"observer={scheme.weights.observer}")
                    print(f"   🔥 GPTQ将处理权重量化")
                else:
                    print(f"   ❌ 无权重配置")
                
                # 检查激活配置
                if hasattr(scheme, 'input_activations') and scheme.input_activations:
                    print(f"   激活配置: {scheme.input_activations.num_bits}位, "
                          f"dynamic={scheme.input_activations.dynamic}")
                    print(f"   ⚠️  GPTQ不处理激活量化，配置将传递给推理引擎")
                else:
                    print(f"   ❌ 无激活配置")
            else:
                print(f"   ❌ 无量化配置")
        
        # 执行原始压缩
        return original_compress(self)
    
    # 应用补丁
    QuantizationMixin.initialize_quantization = tracked_initialize_quantization
    GPTQModifier.compress_modules = tracked_compress_modules
    
    print("✅ GPTQ配置追踪补丁已应用")

def test_gptq_w8a8_processing():
    """测试GPTQ的W8A8处理过程"""
    print("\n🚀 测试GPTQ的W8A8处理过程")
    print("="*80)
    
    # 应用追踪补丁
    patch_gptq_for_config_tracking()
    
    # 加载模型
    MODEL_ID = "/home/<USER>/single_llama"
    model = AutoModelForCausalLM.from_pretrained(MODEL_ID, torch_dtype=torch.float16)
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    # 准备数据
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(8))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=128, truncation=True, add_special_tokens=False)
    
    ds = ds.map(preprocess).map(tokenize, remove_columns=ds.column_names)
    
    # 创建recipe
    recipe = [
        SmoothQuantModifier(smoothing_strength=0.8),
        GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
    ]
    
    print(f"📋 Recipe配置:")
    for i, modifier in enumerate(recipe, 1):
        print(f"   {i}. {type(modifier).__name__}")
        if hasattr(modifier, 'scheme'):
            print(f"      scheme: {modifier.scheme}")
    
    # 执行量化
    print(f"\n🔄 执行量化...")
    from llmcompressor.entrypoints.oneshot import oneshot
    
    try:
        quantized_model = oneshot(
            model=model,
            dataset=ds,
            recipe=recipe,
            max_seq_length=128,
            num_calibration_samples=8,
        )
        
        print(f"✅ 量化成功完成!")
        
        # 分析量化后的模型
        analyze_quantized_model_config(quantized_model)
        
    except Exception as e:
        print(f"❌ 量化失败: {e}")
        import traceback
        traceback.print_exc()

def analyze_quantized_model_config(model):
    """分析量化后模型的配置"""
    print(f"\n📊 分析量化后模型的配置")
    print("="*80)
    
    quantized_layers = []
    
    for name, module in model.named_modules():
        if hasattr(module, 'quantization_scheme'):
            layer_info = {
                'name': name,
                'type': type(module).__name__,
                'has_weight_scale': hasattr(module, 'weight_scale'),
                'has_weight_zero_point': hasattr(module, 'weight_zero_point'),
                'has_quantization_scheme': True
            }
            
            # 检查量化配置
            scheme = module.quantization_scheme
            if hasattr(scheme, 'weights') and scheme.weights:
                layer_info['weights_config'] = {
                    'bits': scheme.weights.num_bits,
                    'dynamic': scheme.weights.dynamic,
                    'strategy': scheme.weights.strategy
                }
            
            if hasattr(scheme, 'input_activations') and scheme.input_activations:
                layer_info['activation_config'] = {
                    'bits': scheme.input_activations.num_bits,
                    'dynamic': scheme.input_activations.dynamic,
                    'strategy': scheme.input_activations.strategy
                }
            
            quantized_layers.append(layer_info)
    
    print(f"🔍 发现 {len(quantized_layers)} 个量化层:")
    for layer in quantized_layers:
        print(f"\n📍 {layer['name']} ({layer['type']}):")
        print(f"   GPTQ处理结果:")
        print(f"     有weight_scale: {layer['has_weight_scale']}")
        print(f"     有weight_zero_point: {layer['has_weight_zero_point']}")
        
        if 'weights_config' in layer:
            wc = layer['weights_config']
            print(f"   权重配置: {wc['bits']}位, dynamic={wc['dynamic']}, strategy={wc['strategy']}")
        
        if 'activation_config' in layer:
            ac = layer['activation_config']
            print(f"   激活配置: {ac['bits']}位, dynamic={ac['dynamic']}, strategy={ac['strategy']}")
            print(f"   ⚠️  激活配置保留，等待推理引擎使用")

def main():
    """主函数"""
    print("🔍 GPTQ如何处理W8A8配置的详细分析")
    print("="*80)
    
    # 1. 分析scheme解析
    w8a8_scheme = analyze_w8a8_scheme_parsing()
    
    # 2. 测试实际处理过程
    test_gptq_w8a8_processing()
    
    print(f"\n🎉 分析完成!")
    print("="*80)
    print("关键发现:")
    print("1. GPTQ将'W8A8'字符串解析为完整的权重+激活配置")
    print("2. GPTQ只处理权重量化部分，使用Hessian矩阵优化")
    print("3. 激活量化配置被保存到模型中，供推理引擎使用")
    print("4. 这种分工实现了高效的W8A8量化：静态权重+动态激活")

if __name__ == "__main__":
    main()
