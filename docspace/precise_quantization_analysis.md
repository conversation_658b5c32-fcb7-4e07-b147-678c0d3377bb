# SmoothQuant + GPTQ 精准量化流程分析报告

生成时间: 2025-08-12 02:31:15

## 1. 执行摘要

- SmoothQuant操作: 4
- GPTQ模块压缩: 7
- GPTQ权重量化: 7

## 2. SmoothQuant阶段分析

### 2.1 激活统计收集

- **model.layers.0.input_layernorm**: 动态范围 [0.119629, 3.902344]
- **model.layers.0.post_attention_layernorm**: 动态范围 [0.403809, 3.027344]

### 2.2 平滑变换应用

#### model.layers.0.input_layernorm

- 平滑因子范围: [0.268555, 4.343750]
- 平衡层变换:
  - Linear: std 0.019974 → 0.041656 (比例: 2.0856)
  - Linear: std 0.019928 → 0.041290 (比例: 2.0720)
  - Linear: std 0.020065 → 0.041077 (比例: 2.0471)

#### model.layers.0.post_attention_layernorm

- 平滑因子范围: [0.771973, 3.908203]
- 平衡层变换:
  - Linear: std 0.019669 → 0.038513 (比例: 1.9581)
  - Linear: std 0.019730 → 0.041779 (比例: 2.1176)

## 3. GPTQ阶段分析

### 3.1 模块: model.layers.0.self_attn.q_proj

- 量化策略: channel
- 校准样本数: 8
- 权重形状: [1024, 16]
- Observer Scale: [0.00022918, 0.00221929]
- Hessian条件数: 4.65e+08
- 量化损失: 0.00244584

#### Per-Channel vs Per-Block冲突解决机制

**关键发现**:
1. GPTQ的块级迭代与per-channel量化完全兼容
2. 每个块内的列量化都正确使用了对应通道的scale
3. Hessian优化过程不改变scale值，只优化量化顺序和权重更新
4. 最终的scale格式与vLLM期望的per-channel格式完全一致

### 3.2 模块: model.layers.0.self_attn.k_proj

- 量化策略: channel
- 校准样本数: 8
- 权重形状: [1024, 16]
- Observer Scale: [0.00025731, 0.00255438]
- Hessian条件数: 4.65e+08
- 量化损失: 0.00251077

#### Per-Channel vs Per-Block冲突解决机制

**关键发现**:
1. GPTQ的块级迭代与per-channel量化完全兼容
2. 每个块内的列量化都正确使用了对应通道的scale
3. Hessian优化过程不改变scale值，只优化量化顺序和权重更新
4. 最终的scale格式与vLLM期望的per-channel格式完全一致

### 3.3 模块: model.layers.0.self_attn.v_proj

- 量化策略: channel
- 校准样本数: 8
- 权重形状: [1024, 16]
- Observer Scale: [0.00021697, 0.00205270]
- Hessian条件数: 4.65e+08
- 量化损失: 0.00240336

#### Per-Channel vs Per-Block冲突解决机制

**关键发现**:
1. GPTQ的块级迭代与per-channel量化完全兼容
2. 每个块内的列量化都正确使用了对应通道的scale
3. Hessian优化过程不改变scale值，只优化量化顺序和权重更新
4. 最终的scale格式与vLLM期望的per-channel格式完全一致

### 3.4 模块: model.layers.0.self_attn.o_proj

- 量化策略: channel
- 校准样本数: 8
- 权重形状: [16, 1024]
- Observer Scale: [0.00047512, 0.00063141]
- Hessian条件数: 1.84e+12
- 量化损失: 0.00000244

#### Per-Channel vs Per-Block冲突解决机制

**关键发现**:
1. GPTQ的块级迭代与per-channel量化完全兼容
2. 每个块内的列量化都正确使用了对应通道的scale
3. Hessian优化过程不改变scale值，只优化量化顺序和权重更新
4. 最终的scale格式与vLLM期望的per-channel格式完全一致

### 3.5 模块: model.layers.0.mlp.gate_proj

- 量化策略: channel
- 校准样本数: 8
- 权重形状: [32, 16]
- Observer Scale: [0.00031547, 0.00104262]
- Hessian条件数: 2.50e+08
- 量化损失: 0.00041414

#### Per-Channel vs Per-Block冲突解决机制

**关键发现**:
1. GPTQ的块级迭代与per-channel量化完全兼容
2. 每个块内的列量化都正确使用了对应通道的scale
3. Hessian优化过程不改变scale值，只优化量化顺序和权重更新
4. 最终的scale格式与vLLM期望的per-channel格式完全一致

### 3.6 模块: model.layers.0.mlp.up_proj

- 量化策略: channel
- 校准样本数: 8
- 权重形状: [32, 16]
- Observer Scale: [0.00034778, 0.00140836]
- Hessian条件数: 2.50e+08
- 量化损失: 0.00050629

#### Per-Channel vs Per-Block冲突解决机制

**关键发现**:
1. GPTQ的块级迭代与per-channel量化完全兼容
2. 每个块内的列量化都正确使用了对应通道的scale
3. Hessian优化过程不改变scale值，只优化量化顺序和权重更新
4. 最终的scale格式与vLLM期望的per-channel格式完全一致

### 3.7 模块: model.layers.0.mlp.down_proj

- 量化策略: channel
- 校准样本数: 8
- 权重形状: [16, 32]
- Observer Scale: [0.00022954, 0.00046123]
- Hessian条件数: 5.42e+09
- 量化损失: 0.00000000

#### Per-Channel vs Per-Block冲突解决机制

**关键发现**:
1. GPTQ的块级迭代与per-channel量化完全兼容
2. 每个块内的列量化都正确使用了对应通道的scale
3. Hessian优化过程不改变scale值，只优化量化顺序和权重更新
4. 最终的scale格式与vLLM期望的per-channel格式完全一致

## 4. 技术结论

### 4.1 SmoothQuant-GPTQ协同机制

1. **SmoothQuant预处理**: 通过激活统计和权重平滑，优化激活分布
2. **GPTQ量化**: 在预处理后的权重上执行Hessian优化的量化
3. **Scale兼容性**: 两阶段的scale处理完全兼容

### 4.2 Per-Channel与Per-Block冲突解决

**问题**: Per-channel量化需要每通道独立scale，Per-block处理按列分块

**解决方案**:
1. Observer阶段计算per-channel scale
2. 块级迭代中，每列量化时使用对应输出通道的scale
3. Hessian优化只影响量化顺序，不改变scale值
4. 最终输出保持per-channel格式

### 4.3 与vLLM的兼容性

- Scale格式: 完全兼容vLLM的per-channel期望
- 权重布局: 符合vLLM的W8A8推理要求
- 精度保证: Hessian优化确保量化精度

