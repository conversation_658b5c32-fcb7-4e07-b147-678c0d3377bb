#!/usr/bin/env python3
"""
真实GPTQ量化流程深度分析 - 基于源码实际执行路径
重点分析per-channel与per-block的具体处理逻辑
"""

import torch
import time
import json
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier
from compressed_tensors.quantization import QuantizationStrategy


class RealGPTQTracker:
    """真实GPTQ执行流程追踪器"""
    
    def __init__(self):
        self.quantize_weight_calls = []
        self.block_iterations = []
        self.scale_analysis = []
        self.hessian_analysis = []
        self.smoothquant_analysis = []
        
    def track_quantize_weight_start(self, module_name, quant_args, W_shape, H_shape, blocksize):
        """追踪quantize_weight函数开始"""
        call_data = {
            'timestamp': time.time(),
            'module_name': module_name,
            'strategy': str(quant_args.strategy),
            'weight_shape': list(W_shape),
            'hessian_shape': list(H_shape),
            'blocksize': blocksize,
            'observer': quant_args.observer,
            'call_id': len(self.quantize_weight_calls)
        }
        self.quantize_weight_calls.append(call_data)
        return call_data['call_id']
    
    def track_observer_scale_calculation(self, call_id, strategy, scale, zero_point, W_stats):
        """追踪Observer scale计算"""
        self.scale_analysis.append({
            'timestamp': time.time(),
            'call_id': call_id,
            'phase': 'observer_calculation',
            'strategy': strategy,
            'scale_shape': list(scale.shape),
            'scale_stats': {
                'min': scale.min().item(),
                'max': scale.max().item(),
                'mean': scale.mean().item(),
                'std': scale.std().item()
            },
            'zero_point_shape': list(zero_point.shape) if zero_point is not None else None,
            'weight_stats': W_stats
        })
    
    def track_block_iteration(self, call_id, block_idx, i1, i2, W1_shape, Hinv1_shape):
        """追踪块级迭代处理"""
        self.block_iterations.append({
            'timestamp': time.time(),
            'call_id': call_id,
            'block_idx': block_idx,
            'column_range': [i1, i2],
            'block_size': i2 - i1,
            'W1_shape': list(W1_shape),
            'Hinv1_shape': list(Hinv1_shape)
        })
    
    def track_column_quantization(self, call_id, block_idx, col_idx, strategy, quantization_details):
        """追踪列级量化"""
        self.scale_analysis.append({
            'timestamp': time.time(),
            'call_id': call_id,
            'phase': 'column_quantization',
            'block_idx': block_idx,
            'column_idx': col_idx,
            'strategy': strategy,
            **quantization_details
        })
    
    def track_hessian_processing(self, call_id, H_condition_number, dampening_applied, inversion_success):
        """追踪Hessian处理"""
        self.hessian_analysis.append({
            'timestamp': time.time(),
            'call_id': call_id,
            'condition_number': H_condition_number,
            'dampening_applied': dampening_applied,
            'inversion_success': inversion_success
        })


def patch_real_gptq_tracking():
    """给真实GPTQ执行路径打补丁"""
    global tracker
    tracker = RealGPTQTracker()
    
    # 1. 劫持quantize_weight函数
    from llmcompressor.modifiers.quantization.gptq import gptq_quantize
    original_quantize_weight = gptq_quantize.quantize_weight
    
    def tracked_quantize_weight(module, quant_args, hessians_dict, blocksize=128, percdamp=0.01):
        module_name = f"{module.__class__.__name__}_{id(module)}"
        W = module.weight.clone()
        H = hessians_dict[module]
        
        print(f"\n🔧 GPTQ quantize_weight: {module_name}")
        print(f"   策略: {quant_args.strategy}")
        print(f"   权重形状: {W.shape}")
        print(f"   Hessian形状: {H.shape}")
        print(f"   块大小: {blocksize}")
        
        # 追踪函数调用开始
        call_id = tracker.track_quantize_weight_start(
            module_name, quant_args, W.shape, H.shape, blocksize
        )
        
        # 执行原始函数，但在关键点插入追踪
        return original_quantize_weight_with_tracking(
            module, quant_args, hessians_dict, blocksize, percdamp, call_id
        )
    
    # 2. 重写quantize_weight以插入详细追踪
    def original_quantize_weight_with_tracking(module, quant_args, hessians_dict, blocksize, percdamp, call_id):
        """带详细追踪的quantize_weight实现"""
        from compressed_tensors.quantization import ActivationOrdering, fake_quantize
        from llmcompressor.observers.base import Observer
        from llmcompressor.modifiers.utils import SPARSITY_THRESHOLD
        from llmcompressor.pytorch.utils.helpers import tensor_sparsity
        from copy import copy
        import transformers
        
        strategy = quant_args.strategy
        actorder = quant_args.actorder
        final_shape = module.weight.shape
        final_dtype = module.weight.dtype
        W = module.weight.clone()
        H = hessians_dict[module]
        del hessians_dict[module]
        
        # 🔥 追踪Observer创建和scale计算
        observer = Observer.load_from_registry(
            quant_args.observer,
            quantization_args=quant_args,
            averaging_constant=1.0,
        )
        
        # 标准化形状和dtype
        if isinstance(module, torch.nn.Conv2d):
            W = W.flatten(1)
        elif isinstance(module, transformers.Conv1D):
            W.transpose_(0, 1)
        W = W.to(dtype=torch.float32)
        num_rows = W.shape[0]
        num_columns = W.shape[1]
        
        print(f"   标准化后权重形状: {W.shape}")
        
        # 🔥 关键：Observer scale计算
        if strategy == QuantizationStrategy.GROUP:
            g_idx = (
                torch.arange(num_columns, device=W.device, dtype=torch.int)
                // quant_args.group_size
            )
            scale, zero_point = observer(W, g_idx=None)
            print(f"   GROUP策略: group_size={quant_args.group_size}, scale形状={scale.shape}")
        else:
            scale, zero_point = observer(W, g_idx=None)
            print(f"   {strategy}策略: scale形状={scale.shape}")
        
        # 追踪Observer scale计算
        tracker.track_observer_scale_calculation(
            call_id, str(strategy), scale, zero_point,
            {
                'mean': W.mean().item(),
                'std': W.std().item(),
                'min': W.min().item(),
                'max': W.max().item()
            }
        )
        
        # 稀疏性处理
        sparsity = tensor_sparsity(W)
        preserve_zeros = sparsity >= SPARSITY_THRESHOLD
        W_nz_mask = (
            (~torch.isclose(W, torch.zeros(1, device=W.device).float())).float()
            if preserve_zeros
            else None
        )
        
        losses = torch.zeros(num_rows, device=module.weight.device)
        
        # 🔥 Hessian处理
        dead = torch.diag(H) == 0
        H[dead, dead] = 1
        W[:, dead] = 0
        
        # 计算条件数
        condition_number = torch.linalg.cond(H).item()
        print(f"   Hessian条件数: {condition_number:.2e}")
        
        # 阻尼和求逆
        try:
            damp = percdamp * torch.mean(torch.diag(H))
            diag = torch.arange(H.shape[0], device=H.device)
            H[diag, diag] += damp
            H = torch.linalg.cholesky(H)
            H = torch.cholesky_inverse(H)
            H = torch.linalg.cholesky(H, upper=True)
            Hinv = H
            inversion_success = True
            print(f"   Hessian求逆成功, 阻尼系数: {damp:.6f}")
        except torch._C._LinAlgError:
            print(f"   Hessian求逆失败, 使用单位矩阵")
            Hinv = H = torch.eye(num_columns, dtype=H.dtype, device=H.device)
            inversion_success = False
        
        # 追踪Hessian处理
        tracker.track_hessian_processing(call_id, condition_number, damp.item() if inversion_success else 0, inversion_success)
        
        # 🔥 核心：块级迭代处理
        print(f"\n   🔍 开始块级迭代处理:")
        print(f"   总列数: {num_columns}, 块大小: {blocksize}")
        
        block_idx = 0
        for i1 in range(0, num_columns, blocksize):
            i2 = min(i1 + blocksize, num_columns)
            count = i2 - i1
            
            print(f"\n     块 {block_idx}: 列[{i1}:{i2}], 大小={count}")
            
            W1 = W[:, i1:i2].clone()
            Q1 = torch.zeros_like(W1)
            Err1 = torch.zeros_like(W1)
            losses1 = torch.zeros_like(W1)
            Hinv1 = Hinv[i1:i2, i1:i2]
            
            # 追踪块迭代
            tracker.track_block_iteration(call_id, block_idx, i1, i2, W1.shape, Hinv1.shape)
            
            if preserve_zeros:
                W1_nz_mask = W_nz_mask[:, i1:i2]
            
            # 🔥 列级量化处理
            for i in range(count):
                w = W1[:, i]
                d = Hinv1[i, i]
                q = w.clone()
                
                column_idx = i1 + i
                
                # 🔥 关键：根据策略进行量化
                if strategy == QuantizationStrategy.TENSOR:
                    q = fake_quantize(q, scale, zero_point, quant_args)
                    quantization_details = {
                        'scale_used': scale.item(),
                        'zero_point_used': zero_point.item() if zero_point is not None else None,
                        'scale_source': 'tensor_scale'
                    }
                    
                elif strategy == QuantizationStrategy.CHANNEL:
                    # 🔥 Per-channel: 每行使用自己的scale
                    q = fake_quantize(q, scale[:, 0], zero_point[:, 0], quant_args)
                    quantization_details = {
                        'scale_used': {
                            'min': scale[:, 0].min().item(),
                            'max': scale[:, 0].max().item(),
                            'shape': list(scale[:, 0].shape)
                        },
                        'zero_point_used': {
                            'min': zero_point[:, 0].min().item() if zero_point is not None else None,
                            'max': zero_point[:, 0].max().item() if zero_point is not None else None
                        } if zero_point is not None else None,
                        'scale_source': 'per_channel_scale'
                    }
                    
                elif strategy == QuantizationStrategy.GROUP:
                    # Group量化处理
                    g_idx = (
                        torch.arange(num_columns, device=W.device, dtype=torch.int)
                        // quant_args.group_size
                    )
                    group_index = g_idx[column_idx]
                    
                    altered_qargs = copy(quant_args)
                    altered_qargs.strategy = QuantizationStrategy.CHANNEL
                    q = fake_quantize(q, scale[:, group_index], zero_point[:, group_index], altered_qargs)
                    quantization_details = {
                        'group_index': group_index.item(),
                        'scale_used': scale[:, group_index].mean().item(),
                        'scale_source': 'group_scale'
                    }
                
                # 追踪列量化
                if i < 3:  # 只记录前几列的详细信息
                    tracker.track_column_quantization(
                        call_id, block_idx, column_idx, str(strategy), quantization_details
                    )
                
                # 误差传播
                Q1[:, i] = q
                losses1[:, i] = (w - q) ** 2 / d**2
                
                if preserve_zeros:
                    Q1[:, i] *= W1_nz_mask[:, i]
                
                Err1[:, i] = (w - q) / d
            
            # 权重更新和误差传播
            W[:, i1:i2] = Q1
            losses[:, i1:i2] = losses1
            
            if i2 < num_columns:
                # 将误差传播到后续列
                W[:, i2:] -= Err1.matmul(Hinv[i1:i2, i2:])
            
            print(f"       块 {block_idx} 完成: 平均损失={losses1.mean().item():.8f}")
            block_idx += 1
        
        # 计算总损失
        total_loss = losses.sum().item()
        
        # 恢复原始形状和dtype
        W = W.to(dtype=final_dtype)
        if isinstance(module, torch.nn.Conv2d):
            W = W.view(final_shape)
        elif isinstance(module, transformers.Conv1D):
            W.transpose_(0, 1)
        
        print(f"   量化完成: 总损失={total_loss:.8f}")
        
        # 为了保持兼容性，返回原始函数期望的格式
        if strategy == QuantizationStrategy.GROUP:
            g_idx = (
                torch.arange(num_columns, device=module.weight.device, dtype=torch.int)
                // quant_args.group_size
            )
            return total_loss, W, scale, zero_point, g_idx
        else:
            return total_loss, W, scale, zero_point, None
    
    # 应用补丁
    gptq_quantize.quantize_weight = tracked_quantize_weight
    
    print("✅ 真实GPTQ追踪补丁已应用")

def test_real_gptq_flow():
    """测试真实GPTQ流程"""
    print("🚀 开始真实GPTQ流程测试")
    print("="*80)
    
    # 应用追踪补丁
    patch_real_gptq_tracking()
    
    # 加载模型
    MODEL_ID = "/home/<USER>/single_llama"
    model = AutoModelForCausalLM.from_pretrained(MODEL_ID, torch_dtype=torch.float16)
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    # 准备数据
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(8))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=128, truncation=True, add_special_tokens=False)
    
    ds = ds.map(preprocess).map(tokenize, remove_columns=ds.column_names)
    
    # 测试不同的量化策略
    strategies_to_test = [
        ("W8A8_CHANNEL", GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"])),
        # ("W4A16_GROUP", GPTQModifier(targets="Linear", scheme="W4A16", group_size=128, ignore=["lm_head"])),
    ]
    
    for strategy_name, modifier in strategies_to_test:
        print(f"\n🔄 测试策略: {strategy_name}")
        print("-"*60)
        
        # 重置追踪器
        global tracker
        tracker = RealGPTQTracker()
        
        # 创建recipe
        recipe = [
            SmoothQuantModifier(smoothing_strength=0.8),
            modifier,
        ]
        
        try:
            from llmcompressor.entrypoints.oneshot import oneshot
            
            quantized_model = oneshot(
                model=model,
                dataset=ds,
                recipe=recipe,
                max_seq_length=128,
                num_calibration_samples=8,
            )
            
            print(f"✅ {strategy_name} 量化成功!")
            
            # 分析结果
            analyze_real_gptq_results(strategy_name)
            
        except Exception as e:
            print(f"❌ {strategy_name} 量化失败: {e}")
            import traceback
            traceback.print_exc()

def analyze_real_gptq_results(strategy_name):
    """分析真实GPTQ结果"""
    print(f"\n📊 {strategy_name} 结果分析")
    print("="*60)
    
    global tracker
    
    print(f"📈 执行统计:")
    print(f"   quantize_weight调用次数: {len(tracker.quantize_weight_calls)}")
    print(f"   块迭代次数: {len(tracker.block_iterations)}")
    print(f"   scale分析记录: {len(tracker.scale_analysis)}")
    print(f"   Hessian分析记录: {len(tracker.hessian_analysis)}")
    
    # 分析每个模块的量化
    for call_data in tracker.quantize_weight_calls:
        call_id = call_data['call_id']
        module_name = call_data['module_name']
        strategy = call_data['strategy']
        
        print(f"\n🔍 模块: {module_name}")
        print(f"   策略: {strategy}")
        print(f"   权重形状: {call_data['weight_shape']}")
        print(f"   块大小: {call_data['blocksize']}")
        
        # 找到相关的scale分析
        observer_scales = [s for s in tracker.scale_analysis if s['call_id'] == call_id and s['phase'] == 'observer_calculation']
        column_quantizations = [s for s in tracker.scale_analysis if s['call_id'] == call_id and s['phase'] == 'column_quantization']
        
        if observer_scales:
            scale_data = observer_scales[0]
            print(f"   Observer Scale统计:")
            print(f"     形状: {scale_data['scale_shape']}")
            print(f"     范围: [{scale_data['scale_stats']['min']:.8f}, {scale_data['scale_stats']['max']:.8f}]")
            print(f"     均值: {scale_data['scale_stats']['mean']:.8f}")
        
        # 分析块级处理
        blocks = [b for b in tracker.block_iterations if b['call_id'] == call_id]
        print(f"   块级处理: {len(blocks)} 个块")
        
        if blocks:
            for i, block in enumerate(blocks[:3]):  # 显示前3个块
                print(f"     块{i}: 列[{block['column_range'][0]}:{block['column_range'][1]}]")
        
        # 分析列级量化（如果有记录）
        if column_quantizations:
            print(f"   列级量化样本: {len(column_quantizations)} 条记录")
            for i, col_quant in enumerate(column_quantizations[:3]):
                print(f"     列{col_quant['column_idx']}: {col_quant['scale_source']}")
    
    # 生成详细报告
    generate_real_gptq_report(strategy_name)

def generate_real_gptq_report(strategy_name):
    """生成真实GPTQ分析报告"""
    global tracker
    
    report_path = f"/workspace/real_gptq_analysis_{strategy_name.lower()}.json"
    
    report_data = {
        'strategy_name': strategy_name,
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'summary': {
            'quantize_weight_calls': len(tracker.quantize_weight_calls),
            'block_iterations': len(tracker.block_iterations),
            'scale_analyses': len(tracker.scale_analysis),
            'hessian_analyses': len(tracker.hessian_analysis)
        },
        'detailed_data': {
            'quantize_weight_calls': tracker.quantize_weight_calls,
            'block_iterations': tracker.block_iterations,
            'scale_analysis': tracker.scale_analysis,
            'hessian_analysis': tracker.hessian_analysis
        }
    }
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 详细报告已生成: {report_path}")

def main():
    """主函数"""
    print("🔍 真实GPTQ量化流程深度分析")
    print("="*80)
    
    test_real_gptq_flow()
    
    print(f"\n🎉 分析完成!")

if __name__ == "__main__":
    main()
