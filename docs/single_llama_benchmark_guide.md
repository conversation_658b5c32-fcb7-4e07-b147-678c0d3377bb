# Single LLaMA Benchmark 完整测试指南

## 概述

本文档详细记录了在单卡GPU上测试single_llama模型benchmark的完整流程，包括环境准备、服务启动、性能测试和问题解决方案。

## 环境信息

### 硬件环境
- GPU: NVIDIA A100-SXM4-40GB (8张卡，使用单卡GPU 3)
- CUDA版本: 12.9
- 驱动版本: 575.51.03

### 软件环境
- Python: 3.10.12
- vLLM: 0.8.5
- 操作系统: Linux (Docker容器)

### 模型信息
- 模型路径: `/workspace/single_llama-W8A8-Dynamic-Per-Token`
- 模型类型: LlamaForCausalLM
- 量化配置: W8A8 (8位权重，8位激活)
- 最大上下文长度: 64 tokens
- 隐藏层数: 1
- 注意力头数: 32

## 测试流程

### 1. 环境检查和准备

#### 1.1 GPU状态检查
```bash
nvidia-smi
```

#### 1.2 Python环境验证
```bash
python3 --version
pip show vllm
```

#### 1.3 模型文件验证
```bash
ls -la /workspace/single_llama-W8A8-Dynamic-Per-Token/
```

确认包含以下文件：
- config.json
- model.safetensors
- tokenizer.json
- tokenizer_config.json
- 其他必要文件

### 2. 服务启动

#### 2.1 配置启动脚本
修改 `scripts/start_single_llama_server.sh` 中的模型路径：
```bash
MODEL_PATH=${MODEL_PATH:-/workspace/single_llama-W8A8-Dynamic-Per-Token}
```

#### 2.2 启动vLLM服务
```bash
CUDA_VISIBLE_DEVICES=3 HOST=127.0.0.1 MAX_MODEL_LEN=64 MAX_BATCHED_TOKENS=64 MAX_NUM_SEQS=8 ./scripts/start_single_llama_server.sh
```

关键参数说明：
- `CUDA_VISIBLE_DEVICES=3`: 指定使用GPU 3
- `HOST=127.0.0.1`: 监听本地地址
- `MAX_MODEL_LEN=64`: 最大序列长度（与模型配置匹配）
- `MAX_BATCHED_TOKENS=64`: 每批最大token数
- `MAX_NUM_SEQS=8`: 最大并发序列数

#### 2.3 服务健康检查
```bash
# 健康检查
curl -s http://127.0.0.1:8000/health

# 模型列表
curl -s http://127.0.0.1:8000/v1/models
```

### 3. 基础功能测试

#### 3.1 简单推理测试
```bash
curl -X POST "http://127.0.0.1:8000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "single_llama",
    "messages": [
      {"role": "user", "content": "你好"}
    ],
    "max_tokens": 10,
    "temperature": 0.0
  }'
```

### 4. Benchmark性能测试

#### 4.1 修改benchmark脚本
由于模型的上下文长度限制（64 tokens），需要修改 `scripts/benchmark_single_llama.py`：

```python
# 修改默认prompt为更短的内容
p.add_argument("--prompt", default="Hi")

# 减少max_tokens默认值
p.add_argument("--max-tokens", type=int, default=8)

# 简化消息格式，移除system message
"messages": [
    {"role": "user", "content": prompt},
],
```

#### 4.2 运行benchmark测试

**基础测试（小规模）：**
```bash
python3 scripts/benchmark_single_llama.py \
  --host 127.0.0.1 \
  --port 8000 \
  --model-name single_llama \
  --concurrency 4 \
  --requests 20 \
  --max-tokens 8 \
  --temperature 0.0
```

**扩展测试（中等规模）：**
```bash
python3 scripts/benchmark_single_llama.py \
  --host 127.0.0.1 \
  --port 8000 \
  --model-name single_llama \
  --concurrency 8 \
  --requests 100 \
  --max-tokens 8 \
  --temperature 0.0
```

**流式输出测试：**
```bash
python3 scripts/benchmark_single_llama.py \
  --host 127.0.0.1 \
  --port 8000 \
  --model-name single_llama \
  --concurrency 4 \
  --requests 20 \
  --max-tokens 8 \
  --temperature 0.0 \
  --stream
```

## 测试结果

### 性能指标

#### 基础测试结果（4并发，20请求）
- **成功率**: 100% (20/20)
- **总耗时**: 0.317秒
- **RPS**: 63.09 请求/秒
- **延迟指标**:
  - P50: 0.052秒
  - P95: 0.123秒
  - P99: 0.123秒
- **吞吐量**: 504.74 tokens/秒

#### 扩展测试结果（8并发，100请求）
- **成功率**: 100% (100/100)
- **总耗时**: 0.725秒
- **RPS**: 137.98 请求/秒
- **延迟指标**:
  - P50: 0.052秒
  - P95: 0.086秒
  - P99: 0.121秒
- **吞吐量**: 1103.86 tokens/秒

#### 流式输出测试结果（4并发，20请求）
- **成功率**: 100% (20/20)
- **总耗时**: 0.237秒
- **RPS**: 84.27 请求/秒
- **延迟指标**:
  - P50: 0.017秒
  - P95: 0.033秒
  - P99: 0.033秒
- **吞吐量**: 530.88 tokens/秒

### 性能分析

1. **高吞吐量**: 在8并发100请求测试中达到1103.86 tokens/秒
2. **低延迟**: P50延迟保持在50ms左右
3. **稳定性**: 所有测试100%成功率，无错误请求
4. **流式优势**: 流式输出显著降低了延迟（P50从52ms降到17ms）

## 问题解决

### 主要问题及解决方案

#### 问题1: 上下文长度超限
**错误信息**: 
```
ValueError: This model's maximum context length is 64 tokens. However, you requested 73 tokens (57 in the messages, 16 in the completion).
```

**解决方案**:
1. 缩短输入prompt（从中文改为"Hi"）
2. 减少max_tokens（从64改为8）
3. 移除system message，简化消息结构

#### 问题2: 服务监听地址配置
**问题**: 服务默认监听容器hostname，外部无法访问

**解决方案**: 
设置环境变量 `HOST=127.0.0.1` 确保服务监听正确地址

## 最佳实践建议

### 1. 服务配置优化
- 根据模型实际配置设置合理的 `MAX_MODEL_LEN`
- 调整 `MAX_BATCHED_TOKENS` 和 `MAX_NUM_SEQS` 平衡吞吐量和延迟
- 使用 `enforce_eager` 模式确保稳定性

### 2. Benchmark测试建议
- 从小规模测试开始，逐步增加负载
- 测试不同并发级别找到最优配置
- 同时测试流式和非流式输出
- 监控GPU内存使用情况

### 3. 生产部署考虑
- 实施适当的负载均衡
- 设置合理的超时时间
- 监控服务健康状态
- 准备故障恢复机制

## 总结

single_llama模型在单卡A100上表现出色：
- 支持高并发请求处理
- 低延迟响应（P50 < 60ms）
- 高吞吐量（>1000 tokens/秒）
- 稳定的服务质量

该测试验证了W8A8量化模型在实际部署中的可行性和性能优势。
