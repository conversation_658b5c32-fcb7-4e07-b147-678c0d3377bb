#!/usr/bin/env python3
"""
基于源码的完整Debug脚本：深度追踪SmoothQuant + GPTQ的量化流程
详细分析每个参数的使用和意义，解析per-channel vs per-block的处理机制
"""

import torch
import time
import copy
import math
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier

class CompleteFlowTracker:
    """完整流程追踪器"""
    
    def __init__(self):
        self.smoothquant_operations = []
        self.gptq_operations = []
        self.scale_transformations = []
        self.hessian_operations = []
        self.block_operations = []
        self.channel_operations = []
        
    def track_smoothquant_operation(self, operation_type, layer_name, data):
        """追踪SmoothQuant操作"""
        self.smoothquant_operations.append({
            'type': operation_type,
            'layer_name': layer_name,
            'data': data,
            'timestamp': time.time()
        })
    
    def track_gptq_operation(self, operation_type, layer_name, data):
        """追踪GPTQ操作"""
        self.gptq_operations.append({
            'type': operation_type,
            'layer_name': layer_name,
            'data': data,
            'timestamp': time.time()
        })
    
    def track_scale_transformation(self, layer_name, before, after, operation):
        """追踪scale变换"""
        self.scale_transformations.append({
            'layer_name': layer_name,
            'before': before,
            'after': after,
            'operation': operation,
            'timestamp': time.time()
        })
    
    def track_block_operation(self, layer_name, block_idx, block_size, operation_data):
        """追踪块级操作"""
        self.block_operations.append({
            'layer_name': layer_name,
            'block_idx': block_idx,
            'block_size': block_size,
            'operation_data': operation_data,
            'timestamp': time.time()
        })
    
    def track_channel_operation(self, layer_name, channel_idx, channel_data):
        """追踪通道级操作"""
        self.channel_operations.append({
            'layer_name': layer_name,
            'channel_idx': channel_idx,
            'channel_data': channel_data,
            'timestamp': time.time()
        })

def patch_smoothquant_for_detailed_tracking():
    """给SmoothQuant打详细追踪补丁"""
    from llmcompressor.modifiers.smoothquant.base import SmoothQuantModifier
    
    global tracker
    tracker = CompleteFlowTracker()
    
    # 1. 追踪激活统计收集
    original_setup_hooks = SmoothQuantModifier._setup_scale_hooks
    
    def tracked_setup_hooks(self):
        print(f"\n🔧 SmoothQuant._setup_scale_hooks 执行")
        print(f"   映射数量: {len(self.resolved_mappings_)}")
        
        def create_tracked_hook(layer_name):
            def hook_fn(module, inp, out):
                if isinstance(out, tuple):
                    out = out[0]
                
                hidden_dim = out.shape[-1]
                out_2d = out.view(-1, hidden_dim)
                
                latest_mins = torch.min(out_2d, dim=0)[0]
                latest_maxes = torch.max(out_2d, dim=0)[0]
                
                # 追踪激活统计
                tracker.track_smoothquant_operation(
                    'activation_stats_collection',
                    layer_name,
                    {
                        'output_shape': list(out.shape),
                        'output_2d_shape': list(out_2d.shape),
                        'mins_range': [latest_mins.min().item(), latest_mins.max().item()],
                        'maxes_range': [latest_maxes.min().item(), latest_maxes.max().item()],
                        'dynamic_range': [(latest_maxes - latest_mins).min().item(), 
                                        (latest_maxes - latest_mins).max().item()]
                    }
                )
                
                # 更新统计
                if layer_name in self.scales_:
                    old_mins = self.scales_[layer_name].min_channel_vals.clone()
                    old_maxes = self.scales_[layer_name].max_channel_vals.clone()
                    
                    self.scales_[layer_name].min_channel_vals = torch.minimum(
                        old_mins, latest_mins
                    )
                    self.scales_[layer_name].max_channel_vals = torch.maximum(
                        old_maxes, latest_maxes
                    )
                    
                    # 追踪统计更新
                    tracker.track_smoothquant_operation(
                        'stats_update',
                        layer_name,
                        {
                            'old_range': [(old_maxes - old_mins).min().item(), 
                                        (old_maxes - old_mins).max().item()],
                            'new_range': [(self.scales_[layer_name].max_channel_vals - 
                                         self.scales_[layer_name].min_channel_vals).min().item(),
                                        (self.scales_[layer_name].max_channel_vals - 
                                         self.scales_[layer_name].min_channel_vals).max().item()]
                        }
                    )
                else:
                    from llmcompressor.modifiers.smoothquant.base import SmoothQuantScale
                    self.scales_[layer_name] = SmoothQuantScale(
                        min_channel_vals=latest_mins,
                        max_channel_vals=latest_maxes
                    )
                    
                    tracker.track_smoothquant_operation(
                        'stats_initialization',
                        layer_name,
                        {
                            'initial_range': [(latest_maxes - latest_mins).min().item(),
                                            (latest_maxes - latest_mins).max().item()]
                        }
                    )
            
            return hook_fn
        
        # 为每个映射注册追踪hook
        for mapping in self.resolved_mappings_:
            name = mapping.smooth_name
            layer = mapping.smooth_layer
            self.register_hook(layer, create_tracked_hook(name), "forward")
            
            tracker.track_smoothquant_operation(
                'hook_registration',
                name,
                {
                    'layer_type': type(layer).__name__,
                    'balance_layers': [type(bl).__name__ for bl in mapping.balance_layers]
                }
            )
    
    # 2. 追踪平滑变换应用
    original_apply_smoothing = SmoothQuantModifier._apply_smoothing
    
    def tracked_apply_smoothing(self, model):
        print(f"\n🔧 SmoothQuant._apply_smoothing 执行")
        
        for mapping in self.resolved_mappings_:
            smooth_name = mapping.smooth_name
            smooth_layer = mapping.smooth_layer
            balance_layers = mapping.balance_layers
            
            print(f"\n📍 处理映射: {smooth_name}")
            print(f"   平滑层: {type(smooth_layer).__name__}")
            print(f"   平衡层数量: {len(balance_layers)}")
            
            # 获取激活动态范围
            activation_scales = (
                self.scales_[smooth_name].max_channel_vals
                - self.scales_[smooth_name].min_channel_vals
            )
            
            tracker.track_smoothquant_operation(
                'activation_scales_calculation',
                smooth_name,
                {
                    'activation_scales_shape': list(activation_scales.shape),
                    'activation_scales_range': [activation_scales.min().item(), 
                                              activation_scales.max().item()],
                    'activation_scales_mean': activation_scales.mean().item(),
                    'activation_scales_std': activation_scales.std().item()
                }
            )
            
            # 计算平滑因子
            scales = self._calculate_smoothing_scales(balance_layers, activation_scales)
            
            tracker.track_smoothquant_operation(
                'smoothing_scales_calculation',
                smooth_name,
                {
                    'smoothing_scales_shape': list(scales.shape),
                    'smoothing_scales_range': [scales.min().item(), scales.max().item()],
                    'smoothing_scales_mean': scales.mean().item(),
                    'smoothing_scales_std': scales.std().item(),
                    'smoothing_strength': self.smoothing_strength
                }
            )
            
            # 应用平滑变换
            @torch.no_grad()
            def smooth(module):
                from compressed_tensors.utils import align_module_device
                with align_module_device(module):
                    if module in balance_layers:
                        # Linear层权重变换
                        old_weight_stats = {
                            'mean': module.weight.mean().item(),
                            'std': module.weight.std().item(),
                            'range': [module.weight.min().item(), module.weight.max().item()]
                        }
                        
                        module.weight.mul_(scales.view(1, -1))
                        
                        new_weight_stats = {
                            'mean': module.weight.mean().item(),
                            'std': module.weight.std().item(),
                            'range': [module.weight.min().item(), module.weight.max().item()]
                        }
                        
                        layer_name = None
                        for name, mod in model.named_modules():
                            if mod is module:
                                layer_name = name
                                break
                        
                        tracker.track_scale_transformation(
                            layer_name or 'unknown',
                            old_weight_stats,
                            new_weight_stats,
                            'linear_weight_scaling'
                        )
                        
                    elif module == smooth_layer:
                        # LayerNorm层权重变换
                        old_weight_stats = {
                            'mean': module.weight.mean().item(),
                            'std': module.weight.std().item(),
                            'range': [module.weight.min().item(), module.weight.max().item()]
                        }
                        
                        if module.weight.ndim == 1:
                            module.weight.div_(scales)
                        else:
                            module.weight.div_(scales.view(-1, 1))
                        
                        if hasattr(module, "bias") and module.bias is not None:
                            module.bias.div_(scales)
                        
                        new_weight_stats = {
                            'mean': module.weight.mean().item(),
                            'std': module.weight.std().item(),
                            'range': [module.weight.min().item(), module.weight.max().item()]
                        }
                        
                        tracker.track_scale_transformation(
                            smooth_name,
                            old_weight_stats,
                            new_weight_stats,
                            'layernorm_weight_scaling'
                        )
            
            # 应用到所有相关模块
            smooth(smooth_layer)
            for balance_layer in balance_layers:
                smooth(balance_layer)
            
            # 清理统计数据
            del self.scales_[smooth_name]
            
            tracker.track_smoothquant_operation(
                'smoothing_complete',
                smooth_name,
                {'status': 'completed'}
            )
    
    # 应用补丁
    SmoothQuantModifier._setup_scale_hooks = tracked_setup_hooks
    SmoothQuantModifier._apply_smoothing = tracked_apply_smoothing
    
    print("✅ SmoothQuant详细追踪补丁已应用")

def patch_gptq_for_detailed_tracking():
    """给GPTQ打详细追踪补丁"""
    from llmcompressor.modifiers.quantization.gptq.base import GPTQModifier
    from llmcompressor.modifiers.quantization.gptq.gptq_quantize import quantize_weight
    
    # 1. 追踪GPTQ初始化
    original_initialize = GPTQModifier.initialize_quantization
    
    def tracked_initialize(self, model):
        print(f"\n🔧 GPTQ.initialize_quantization 执行")
        
        result = original_initialize(self, model)
        
        # 检查量化配置
        for name, module in model.named_modules():
            if hasattr(module, 'quantization_scheme'):
                scheme = module.quantization_scheme
                
                tracker.track_gptq_operation(
                    'quantization_scheme_setup',
                    name,
                    {
                        'module_type': type(module).__name__,
                        'has_weights_config': hasattr(scheme, 'weights') and scheme.weights is not None,
                        'has_activation_config': hasattr(scheme, 'input_activations') and scheme.input_activations is not None,
                        'weights_strategy': scheme.weights.strategy.value if hasattr(scheme, 'weights') and scheme.weights else None,
                        'weights_bits': scheme.weights.num_bits if hasattr(scheme, 'weights') and scheme.weights else None,
                        'activation_strategy': scheme.input_activations.strategy.value if hasattr(scheme, 'input_activations') and scheme.input_activations else None,
                        'activation_bits': scheme.input_activations.num_bits if hasattr(scheme, 'input_activations') and scheme.input_activations else None
                    }
                )
        
        return result
    
    # 2. 追踪权重量化
    original_quantize_weight = quantize_weight
    
    def tracked_quantize_weight(module, quant_args, hessians_dict, blocksize, percdamp):
        module_name = None
        for name, mod in module.named_modules():
            if mod is module:
                module_name = name
                break
        if module_name is None:
            # 如果在模块内部找不到，尝试从全局查找
            import gc
            for obj in gc.get_objects():
                if hasattr(obj, 'named_modules'):
                    for name, mod in obj.named_modules():
                        if mod is module:
                            module_name = name
                            break
                    if module_name:
                        break
        
        if module_name is None:
            module_name = f"unknown_{type(module).__name__}"
        
        print(f"\n🔧 quantize_weight 执行: {module_name}")
        print(f"   权重形状: {module.weight.shape}")
        print(f"   量化策略: {quant_args.strategy}")
        print(f"   块大小: {blocksize}")
        print(f"   阻尼系数: {percdamp}")
        
        # 记录量化前的权重统计
        pre_quant_stats = {
            'weight_shape': list(module.weight.shape),
            'weight_mean': module.weight.mean().item(),
            'weight_std': module.weight.std().item(),
            'weight_range': [module.weight.min().item(), module.weight.max().item()],
            'strategy': quant_args.strategy.value,
            'num_bits': quant_args.num_bits,
            'symmetric': quant_args.symmetric,
            'block_size': blocksize,
            'percdamp': percdamp
        }
        
        tracker.track_gptq_operation(
            'pre_quantization_analysis',
            module_name,
            pre_quant_stats
        )
        
        # 执行原始量化
        loss, quantized_weight, scale, zero_point, g_idx = original_quantize_weight(
            module, quant_args, hessians_dict, blocksize, percdamp
        )
        
        # 记录量化后的统计
        post_quant_stats = {
            'quantized_weight_shape': list(quantized_weight.shape),
            'quantized_weight_dtype': str(quantized_weight.dtype),
            'scale_shape': list(scale.shape),
            'scale_range': [scale.min().item(), scale.max().item()],
            'scale_mean': scale.mean().item(),
            'scale_std': scale.std().item(),
            'zero_point_shape': list(zero_point.shape),
            'zero_point_range': [zero_point.min().item(), zero_point.max().item()],
            'quantization_loss': loss,
            'g_idx_shape': list(g_idx.shape) if g_idx is not None else None
        }
        
        tracker.track_gptq_operation(
            'post_quantization_analysis',
            module_name,
            post_quant_stats
        )
        
        return loss, quantized_weight, scale, zero_point, g_idx
    
    # 应用补丁
    GPTQModifier.initialize_quantization = tracked_initialize
    
    # 替换quantize_weight函数
    import llmcompressor.modifiers.quantization.gptq.gptq_quantize as gptq_module
    gptq_module.quantize_weight = tracked_quantize_weight
    
    print("✅ GPTQ详细追踪补丁已应用")

def run_complete_flow_debug():
    """运行完整流程debug"""
    print("🚀 开始完整流程debug")
    print("="*80)
    
    # 应用追踪补丁
    patch_smoothquant_for_detailed_tracking()
    patch_gptq_for_detailed_tracking()
    
    # 加载模型
    MODEL_ID = "/home/<USER>/single_llama"
    model = AutoModelForCausalLM.from_pretrained(MODEL_ID, torch_dtype=torch.float16)
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    # 准备数据
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(8))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=128, truncation=True, add_special_tokens=False)
    
    ds = ds.map(preprocess).map(tokenize, remove_columns=ds.column_names)
    
    # 创建recipe
    recipe = [
        SmoothQuantModifier(smoothing_strength=0.8),
        GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
    ]
    
    print(f"📋 Recipe配置:")
    for i, modifier in enumerate(recipe, 1):
        print(f"   {i}. {type(modifier).__name__}")
    
    # 执行量化
    print(f"\n🔄 执行量化...")
    from llmcompressor.entrypoints.oneshot import oneshot
    
    try:
        quantized_model = oneshot(
            model=model,
            dataset=ds,
            recipe=recipe,
            max_seq_length=128,
            num_calibration_samples=8,
        )
        
        print(f"✅ 量化成功完成!")
        
        # 分析追踪结果
        analyze_complete_flow()
        
        return quantized_model
        
    except Exception as e:
        print(f"❌ 量化失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_complete_flow():
    """分析完整流程"""
    print(f"\n📊 完整流程分析")
    print("="*80)
    
    global tracker
    
    # 1. SmoothQuant操作分析
    print(f"\n🔍 SmoothQuant操作分析:")
    print(f"   总操作数: {len(tracker.smoothquant_operations)}")
    
    operation_types = {}
    for op in tracker.smoothquant_operations:
        op_type = op['type']
        operation_types[op_type] = operation_types.get(op_type, 0) + 1
    
    for op_type, count in operation_types.items():
        print(f"   {op_type}: {count}次")
    
    # 2. GPTQ操作分析
    print(f"\n🔍 GPTQ操作分析:")
    print(f"   总操作数: {len(tracker.gptq_operations)}")
    
    gptq_operation_types = {}
    for op in tracker.gptq_operations:
        op_type = op['type']
        gptq_operation_types[op_type] = gptq_operation_types.get(op_type, 0) + 1
    
    for op_type, count in gptq_operation_types.items():
        print(f"   {op_type}: {count}次")
    
    # 3. Scale变换分析
    print(f"\n🔍 Scale变换分析:")
    print(f"   总变换数: {len(tracker.scale_transformations)}")
    
    for transform in tracker.scale_transformations:
        print(f"\n   📍 {transform['layer_name']} ({transform['operation']}):")
        before = transform['before']
        after = transform['after']
        
        print(f"      变换前: mean={before['mean']:.6f}, std={before['std']:.6f}")
        print(f"      变换后: mean={after['mean']:.6f}, std={after['std']:.6f}")
        
        if before['std'] > 0:
            std_change = (after['std'] - before['std']) / before['std'] * 100
            print(f"      std变化: {std_change:+.2f}%")
    
    # 4. 详细的量化参数分析
    print(f"\n🔍 详细量化参数分析:")
    
    for op in tracker.gptq_operations:
        if op['type'] == 'post_quantization_analysis':
            layer_name = op['layer_name']
            data = op['data']
            
            print(f"\n   📍 {layer_name}:")
            print(f"      量化权重: {data['quantized_weight_shape']} {data['quantized_weight_dtype']}")
            print(f"      scale: {data['scale_shape']}, 范围[{data['scale_range'][0]:.8f}, {data['scale_range'][1]:.8f}]")
            print(f"      scale统计: mean={data['scale_mean']:.8f}, std={data['scale_std']:.8f}")
            print(f"      zero_point: {data['zero_point_shape']}, 范围[{data['zero_point_range'][0]}, {data['zero_point_range'][1]}]")
            print(f"      量化损失: {data['quantization_loss']:.8f}")

def main():
    """主函数"""
    print("🔍 SmoothQuant + GPTQ完整流程深度debug")
    print("="*80)
    
    quantized_model = run_complete_flow_debug()
    
    if quantized_model:
        print(f"\n🎉 Debug完成!")
        print("="*80)
        print("关键发现将在分析报告中详细说明")

if __name__ == "__main__":
    main()
