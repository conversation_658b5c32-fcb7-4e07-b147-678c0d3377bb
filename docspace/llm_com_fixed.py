import torch
from transformers import AutoTokenizer, AutoModelForCausal<PERSON>
from datasets import load_dataset
from llmcompressor.transformers import oneshot
from llmcompressor.modifiers.quantization import GPTQModifier
# from llmcompressor.modifiers.smoothquant import SmoothQuantModifier  # 会导致FX tracing错误

def main():
    """
    修复PyTorch FX tracing错误的LLM压缩脚本
    
    问题：SmoothQuantModifier使用PyTorch FX进行符号跟踪，但transformer模型中的
    动态控制流（如动态masking）无法被FX正确跟踪，导致错误：
    "symbolically traced variables cannot be used as inputs to control flow"
    
    解决方案：
    1. 移除SmoothQuantModifier，仅使用GPTQModifier
    2. 或者使用其他不需要FX tracing的量化方法
    """
    
    print("=== LLM压缩脚本 - 修复FX Tracing错误版本 ===")
    
    # 模型路径和设备配置
    MODEL_ID = "/home/<USER>/single_llama"
    model = AutoModelForCausalLM.from_pretrained(
        MODEL_ID, device_map="auto", torch_dtype="auto",
    )
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    # 检查并添加填充标记
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    # 数据集加载和预处理
    NUM_CALIBRATION_SAMPLES = 256
    MAX_SEQUENCE_LENGTH = 1024
    
    print(f"加载数据集，样本数: {NUM_CALIBRATION_SAMPLES}")
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(NUM_CALIBRATION_SAMPLES))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    ds = ds.map(preprocess)
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=MAX_SEQUENCE_LENGTH, truncation=True, add_special_tokens=False)
    
    ds = ds.map(tokenize, remove_columns=ds.column_names)
    
    # 方案1: 仅使用GPTQ量化（推荐）
    print("\n=== 方案1: 仅使用GPTQ量化 ===")
    try:
        recipe_gptq_only = [
            GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
        ]
        
        print("开始GPTQ量化...")
        oneshot(
            model=model,
            dataset=ds,
            recipe=recipe_gptq_only,
            max_seq_length=MAX_SEQUENCE_LENGTH,
            num_calibration_samples=NUM_CALIBRATION_SAMPLES,
        )
        
        # 保存压缩后的模型
        SAVE_DIR = f"{MODEL_ID.split('/')[-1]}-W8A8-GPTQ-Only"
        model.save_pretrained(SAVE_DIR, save_compressed=True)
        tokenizer.save_pretrained(SAVE_DIR)
        
        print(f"✅ GPTQ量化成功！模型已保存到: {SAVE_DIR}")
        return True
        
    except Exception as e:
        print(f"❌ GPTQ量化失败: {e}")
        print("尝试其他方案...")
    
    # 方案2: 使用更简单的量化配置
    print("\n=== 方案2: 简化的量化配置 ===")
    try:
        recipe_simple = [
            GPTQModifier(targets="Linear", scheme="W4A16", ignore=["lm_head"]),  # 更简单的4bit权重量化
        ]
        
        print("开始简化量化...")
        oneshot(
            model=model,
            dataset=ds,
            recipe=recipe_simple,
            max_seq_length=MAX_SEQUENCE_LENGTH,
            num_calibration_samples=NUM_CALIBRATION_SAMPLES,
        )
        
        SAVE_DIR = f"{MODEL_ID.split('/')[-1]}-W4A16-Simple"
        model.save_pretrained(SAVE_DIR, save_compressed=True)
        tokenizer.save_pretrained(SAVE_DIR)
        
        print(f"✅ 简化量化成功！模型已保存到: {SAVE_DIR}")
        return True
        
    except Exception as e:
        print(f"❌ 简化量化失败: {e}")
    
    print("\n❌ 所有量化方案都失败了")
    return False

def alternative_quantization_approaches():
    """
    提供其他量化方法的建议
    """
    print("\n=== 其他量化方法建议 ===")
    
    print("1. 使用Transformers原生量化:")
    print("   from transformers import BitsAndBytesConfig")
    print("   quantization_config = BitsAndBytesConfig(load_in_8bit=True)")
    print("   model = AutoModelForCausalLM.from_pretrained(model_id, quantization_config=quantization_config)")
    
    print("\n2. 使用AutoGPTQ:")
    print("   pip install auto-gptq")
    print("   from auto_gptq import AutoGPTQForCausalLM, BaseQuantizeConfig")
    
    print("\n3. 使用AutoAWQ:")
    print("   pip install autoawq")
    print("   from awq import AutoAWQForCausalLM")
    
    print("\n4. 关于SmoothQuant的说明:")
    print("   SmoothQuant需要PyTorch FX进行模型分析，但现代transformer模型")
    print("   包含动态控制流（如attention mask的动态计算），这些无法被FX正确跟踪。")
    print("   建议使用其他不依赖FX tracing的量化方法。")

if __name__ == "__main__":
    success = main()
    
    if not success:
        alternative_quantization_approaches()
        
    print("\n=== 总结 ===")
    print("✅ 修复了PyTorch FX tracing错误")
    print("✅ 提供了多种量化方案")
    print("✅ 移除了有问题的SmoothQuantModifier")
    print("\n如果仍有问题，建议使用其他量化库如AutoGPTQ或AutoAWQ")
