# 量化代码调试与问题排查指南

## 🔍 常见问题诊断

### 1. FX Tracing错误
```python
# 错误信息
RuntimeError: symbolically traced variables cannot be used as inputs to control flow

# 问题定位
error_location = {
    "file": "llmcompressor/pipelines/sequential/ast_utils/auto_wrapper.py",
    "function": "_wrap_if_possible", 
    "line": 183-184,
    "code": "if (input_ids is None) ^ (inputs_embeds is not None):"
}

# 解决方案
solutions = [
    "降级PyTorch版本: pip install torch==2.5.1+cu124",
    "设置环境变量: os.environ['TORCH_FX_DISABLE'] = '1'",
    "使用显式mappings避免自动推断"
]

# 验证修复
def verify_fix():
    import torch
    print(f"PyTorch版本: {torch.__version__}")
    assert torch.__version__.startswith('2.5'), "需要PyTorch 2.5.x版本"
```

### 2. 内存不足错误
```python
# 错误信息
RuntimeError: CUDA out of memory

# 问题分析
memory_analysis = {
    "模型权重": "68,144参数 × 2字节 = 0.13MB",
    "激活缓存": "32样本 × 512序列 × 16维 × 2字节 = 0.65MB", 
    "量化临时数据": "约2-3倍模型大小",
    "总需求": "约2-5MB (理论值)"
}

# 解决策略
memory_solutions = {
    "减少校准样本": "num_calibration_samples = 16",
    "缩短序列长度": "max_sequence_length = 256", 
    "使用CPU卸载": "device_map = 'cpu'",
    "清理GPU缓存": "torch.cuda.empty_cache()"
}
```

### 3. 量化精度下降
```python
# 问题检测
def check_quantization_quality(original_model, quantized_model, test_data):
    """检查量化质量"""
    
    # 1. 权重分布对比
    for name, param in original_model.named_parameters():
        if name in quantized_model.state_dict():
            orig_std = param.std().item()
            quant_std = quantized_model.state_dict()[name].float().std().item()
            ratio = quant_std / orig_std
            
            if ratio < 0.5 or ratio > 2.0:
                print(f"⚠️ {name}: 权重分布异常 (比例: {ratio:.2f})")
    
    # 2. 输出一致性检查
    with torch.no_grad():
        orig_output = original_model(**test_data)
        quant_output = quantized_model(**test_data)
        
        mse = torch.nn.functional.mse_loss(orig_output.logits, quant_output.logits)
        print(f"输出MSE: {mse:.6f}")
        
        if mse > 0.01:
            print("⚠️ 量化精度可能过低")

# 精度优化策略
precision_improvements = {
    "降低平滑强度": "smoothing_strength = 0.5",
    "使用更高精度": "scheme = 'W8A16'",
    "增加校准样本": "num_calibration_samples = 1024",
    "调整阻尼系数": "dampening_frac = 0.001"
}
```

## 🛠️ 调试工具与技巧

### 1. 详细日志配置
```python
# 启用详细日志
import logging

# 设置llmcompressor日志级别
logging.getLogger("llmcompressor").setLevel(logging.DEBUG)

# 设置特定模块的日志
debug_modules = [
    "llmcompressor.core.lifecycle",
    "llmcompressor.modifiers.smoothquant", 
    "llmcompressor.modifiers.quantization.gptq",
    "llmcompressor.pipelines.sequential"
]

for module in debug_modules:
    logging.getLogger(module).setLevel(logging.DEBUG)

# 自定义日志格式
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

### 2. 中间结果检查
```python
# 在量化过程中插入检查点
class QuantizationDebugger:
    def __init__(self):
        self.checkpoints = {}
    
    def save_checkpoint(self, name, data):
        """保存中间结果"""
        self.checkpoints[name] = {
            'data': data.clone() if torch.is_tensor(data) else data,
            'timestamp': time.time(),
            'memory_usage': torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        }
        print(f"✓ 检查点保存: {name}")
    
    def compare_checkpoints(self, name1, name2):
        """对比两个检查点"""
        if name1 in self.checkpoints and name2 in self.checkpoints:
            data1 = self.checkpoints[name1]['data']
            data2 = self.checkpoints[name2]['data']
            
            if torch.is_tensor(data1) and torch.is_tensor(data2):
                diff = torch.nn.functional.mse_loss(data1.float(), data2.float())
                print(f"检查点对比 {name1} vs {name2}: MSE = {diff:.6f}")
            
    def export_report(self, filename):
        """导出调试报告"""
        report = {
            'checkpoints': list(self.checkpoints.keys()),
            'timeline': [(name, cp['timestamp']) for name, cp in self.checkpoints.items()],
            'memory_usage': [(name, cp['memory_usage']) for name, cp in self.checkpoints.items()]
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)

# 使用示例
debugger = QuantizationDebugger()

# 在关键位置插入检查点
def debug_oneshot(model, dataset, recipe, **kwargs):
    debugger.save_checkpoint('original_model', model.state_dict())
    
    # 执行SmoothQuant
    # ... (在SmoothQuant后)
    debugger.save_checkpoint('after_smoothquant', model.state_dict())
    
    # 执行GPTQ
    # ... (在GPTQ后)
    debugger.save_checkpoint('after_gptq', model.state_dict())
    
    # 对比结果
    debugger.compare_checkpoints('original_model', 'after_smoothquant')
    debugger.compare_checkpoints('after_smoothquant', 'after_gptq')
    
    # 导出报告
    debugger.export_report('quantization_debug_report.json')
```

### 3. 性能分析工具
```python
# 性能分析装饰器
import functools
import time

def profile_function(func):
    """函数性能分析装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        result = func(*args, **kwargs)
        
        end_time = time.time()
        end_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        print(f"🔍 {func.__name__}:")
        print(f"   时间: {end_time - start_time:.3f}s")
        print(f"   内存变化: {(end_memory - start_memory) / 1024**2:.2f}MB")
        
        return result
    return wrapper

# 使用示例
@profile_function
def quantize_layer(layer, calibration_data):
    # 量化逻辑
    pass
```

## 📊 监控指标

### 1. 量化质量指标
```python
class QuantizationMetrics:
    def __init__(self):
        self.metrics = {}
    
    def compute_weight_metrics(self, original_weights, quantized_weights):
        """计算权重量化指标"""
        metrics = {}
        
        for name in original_weights:
            if name in quantized_weights:
                orig = original_weights[name].float()
                quant = quantized_weights[name].float()
                
                # 均方误差
                mse = torch.nn.functional.mse_loss(orig, quant).item()
                
                # 信噪比
                signal_power = torch.mean(orig ** 2).item()
                noise_power = torch.mean((orig - quant) ** 2).item()
                snr = 10 * torch.log10(signal_power / (noise_power + 1e-8)).item()
                
                # 相关系数
                correlation = torch.corrcoef(torch.stack([orig.flatten(), quant.flatten()]))[0, 1].item()
                
                metrics[name] = {
                    'mse': mse,
                    'snr_db': snr,
                    'correlation': correlation
                }
        
        return metrics
    
    def compute_model_size(self, model):
        """计算模型大小"""
        total_params = 0
        total_size = 0
        
        for name, param in model.named_parameters():
            num_params = param.numel()
            param_size = num_params * param.element_size()
            
            total_params += num_params
            total_size += param_size
            
        return {
            'total_params': total_params,
            'total_size_mb': total_size / 1024**2
        }
```

### 2. 实时监控
```python
# 实时监控类
class RealTimeMonitor:
    def __init__(self, update_interval=1.0):
        self.update_interval = update_interval
        self.metrics_history = []
        self.running = False
    
    def start_monitoring(self):
        """开始监控"""
        self.running = True
        
        def monitor_loop():
            while self.running:
                metrics = self.collect_metrics()
                self.metrics_history.append(metrics)
                time.sleep(self.update_interval)
        
        import threading
        self.monitor_thread = threading.Thread(target=monitor_loop)
        self.monitor_thread.start()
    
    def collect_metrics(self):
        """收集当前指标"""
        metrics = {
            'timestamp': time.time(),
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent
        }
        
        if torch.cuda.is_available():
            metrics.update({
                'gpu_memory_allocated': torch.cuda.memory_allocated(),
                'gpu_memory_reserved': torch.cuda.memory_reserved(),
                'gpu_utilization': get_gpu_utilization()
            })
        
        return metrics
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join()
    
    def export_metrics(self, filename):
        """导出监控数据"""
        with open(filename, 'w') as f:
            json.dump(self.metrics_history, f, indent=2)
```

## 🎯 最佳实践建议

### 1. 开发环境配置
```python
# 推荐的开发环境设置
development_setup = {
    "pytorch_version": "2.5.1+cu124",
    "python_version": "3.8+",
    "cuda_version": "12.4",
    "memory_requirements": "至少8GB GPU内存",
    "disk_space": "至少10GB可用空间"
}

# 环境验证脚本
def verify_environment():
    """验证开发环境"""
    checks = []
    
    # PyTorch版本检查
    import torch
    torch_version = torch.__version__
    if torch_version.startswith('2.5'):
        checks.append(("✓", f"PyTorch版本: {torch_version}"))
    else:
        checks.append(("✗", f"PyTorch版本不兼容: {torch_version}"))
    
    # CUDA检查
    if torch.cuda.is_available():
        cuda_version = torch.version.cuda
        checks.append(("✓", f"CUDA可用: {cuda_version}"))
    else:
        checks.append(("✗", "CUDA不可用"))
    
    # 内存检查
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        if gpu_memory >= 8:
            checks.append(("✓", f"GPU内存充足: {gpu_memory:.1f}GB"))
        else:
            checks.append(("⚠", f"GPU内存可能不足: {gpu_memory:.1f}GB"))
    
    # 打印结果
    for status, message in checks:
        print(f"{status} {message}")
    
    return all(check[0] == "✓" for check in checks)
```

### 2. 代码组织建议
```python
# 推荐的项目结构
project_structure = """
quantization_project/
├── configs/
│   ├── smoothquant_w8a8.yaml
│   ├── gptq_w4a16.yaml
│   └── debug_config.yaml
├── scripts/
│   ├── quantize_model.py
│   ├── evaluate_model.py
│   └── debug_quantization.py
├── utils/
│   ├── data_utils.py
│   ├── model_utils.py
│   └── debug_utils.py
├── tests/
│   ├── test_quantization.py
│   └── test_precision.py
└── outputs/
    ├── quantized_models/
    ├── logs/
    └── reports/
"""

# 配置文件模板
config_template = """
# smoothquant_w8a8.yaml
quantization:
  method: "smoothquant_gptq"
  smoothquant:
    smoothing_strength: 0.8
    mappings: null  # auto-infer
  gptq:
    scheme: "W8A8"
    targets: ["Linear"]
    ignore: ["lm_head"]
    block_size: 128
    dampening_frac: 0.01

data:
  dataset_path: "/path/to/calibration/data"
  num_samples: 512
  max_length: 512

model:
  model_path: "/path/to/model"
  output_dir: "/path/to/output"

debug:
  enable_logging: true
  save_checkpoints: true
  monitor_memory: true
"""
```

这个调试指南提供了完整的问题诊断、调试工具和最佳实践，帮助开发者快速定位和解决量化过程中的各种问题。
