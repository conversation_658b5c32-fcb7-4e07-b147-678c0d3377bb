nohup: ignoring input
========== nvidia-smi ==========
Wed Sep 10 06:15:43 2025       
+-----------------------------------------------------------------------------------------+
| NVIDIA-SMI 575.51.03              Driver Version: 575.51.03      CUDA Version: 12.9     |
|-----------------------------------------+------------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id          Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |           Memory-Usage | GPU-Util  Compute M. |
|                                         |                        |               MIG M. |
|=========================================+========================+======================|
|   0  NVIDIA A100-SXM4-40GB          Off |   00000000:0E:00.0 Off |                    0 |
| N/A   29C    P0             60W /  400W |     440MiB /  40960MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
|   1  NVIDIA A100-SXM4-40GB          Off |   00000000:0F:00.0 Off |                    0 |
| N/A   30C    P0             65W /  400W |    6279MiB /  40960MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
|   2  NVIDIA A100-SXM4-40GB          Off |   00000000:1F:00.0 Off |                    0 |
| N/A   31C    P0             83W /  400W |    6135MiB /  40960MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
|   3  NVIDIA A100-SXM4-40GB          Off |   00000000:20:00.0 Off |                    0 |
| N/A   30C    P0             72W /  400W |     435MiB /  40960MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
|   4  NVIDIA A100-SXM4-40GB          Off |   00000000:B5:00.0 Off |                    0 |
| N/A   30C    P0             76W /  400W |    7046MiB /  40960MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
|   5  NVIDIA A100-SXM4-40GB          Off |   00000000:B6:00.0 Off |                    0 |
| N/A   30C    P0             75W /  400W |    5775MiB /  40960MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
|   6  NVIDIA A100-SXM4-40GB          Off |   00000000:CE:00.0 Off |                    0 |
| N/A   29C    P0             76W /  400W |    6279MiB /  40960MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
|   7  NVIDIA A100-SXM4-40GB          Off |   00000000:CF:00.0 Off |                    0 |
| N/A   30C    P0             76W /  400W |    6281MiB /  40960MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+
                                                                                         
+-----------------------------------------------------------------------------------------+
| Processes:                                                                              |
|  GPU   GI   CI              PID   Type   Process name                        GPU Memory |
|        ID   ID                                                               Usage      |
|=========================================================================================|
|  No running processes found                                                             |
+-----------------------------------------------------------------------------------------+
================================
Starting vLLM OpenAI server...
MODEL_PATH=/home/<USER>/single_llama
SERVED_MODEL_NAME=single_llama
HOST=0.0.0.0 PORT=8000 TP_SIZE=1
GPU_MEM_UTIL=0.90 MAX_MODEL_LEN=2048
MAX_BATCHED_TOKENS=2048 MAX_NUM_SEQS=32
INFO 09-10 06:15:51 [__init__.py:239] Automatically detected platform cuda.
usage: api_server.py [-h] [--host HOST] [--port PORT]
                     [--uvicorn-log-level {debug,info,warning,error,critical,trace}]
                     [--disable-uvicorn-access-log] [--allow-credentials]
                     [--allowed-origins ALLOWED_ORIGINS]
                     [--allowed-methods ALLOWED_METHODS]
                     [--allowed-headers ALLOWED_HEADERS] [--api-key API_KEY]
                     [--lora-modules LORA_MODULES [LORA_MODULES ...]]
                     [--prompt-adapters PROMPT_ADAPTERS [PROMPT_ADAPTERS ...]]
                     [--chat-template CHAT_TEMPLATE]
                     [--chat-template-content-format {auto,string,openai}]
                     [--response-role RESPONSE_ROLE]
                     [--ssl-keyfile SSL_KEYFILE] [--ssl-certfile SSL_CERTFILE]
                     [--ssl-ca-certs SSL_CA_CERTS] [--enable-ssl-refresh]
                     [--ssl-cert-reqs SSL_CERT_REQS] [--root-path ROOT_PATH]
                     [--middleware MIDDLEWARE] [--return-tokens-as-token-ids]
                     [--disable-frontend-multiprocessing]
                     [--enable-request-id-headers] [--enable-auto-tool-choice]
                     [--tool-call-parser {granite-20b-fc,granite,hermes,internlm,jamba,llama4_json,llama3_json,mistral,phi4_mini_json,pythonic} or name registered in --tool-parser-plugin]
                     [--tool-parser-plugin TOOL_PARSER_PLUGIN] [--model MODEL]
                     [--task {auto,generate,embedding,embed,classify,score,reward,transcription}]
                     [--tokenizer TOKENIZER] [--hf-config-path HF_CONFIG_PATH]
                     [--skip-tokenizer-init] [--revision REVISION]
                     [--code-revision CODE_REVISION]
                     [--tokenizer-revision TOKENIZER_REVISION]
                     [--tokenizer-mode {auto,slow,mistral,custom}]
                     [--trust-remote-code]
                     [--allowed-local-media-path ALLOWED_LOCAL_MEDIA_PATH]
                     [--load-format {auto,pt,safetensors,npcache,dummy,tensorizer,sharded_state,gguf,bitsandbytes,mistral,runai_streamer,runai_streamer_sharded,fastsafetensors}]
                     [--download-dir DOWNLOAD_DIR]
                     [--model-loader-extra-config MODEL_LOADER_EXTRA_CONFIG]
                     [--use-tqdm-on-load | --no-use-tqdm-on-load]
                     [--config-format {auto,hf,mistral}]
                     [--dtype {auto,half,float16,bfloat16,float,float32}]
                     [--max-model-len MAX_MODEL_LEN]
                     [--guided-decoding-backend {auto,guidance,xgrammar}]
                     [--reasoning-parser {deepseek_r1,granite}]
                     [--logits-processor-pattern LOGITS_PROCESSOR_PATTERN]
                     [--model-impl {auto,vllm,transformers}]
                     [--distributed-executor-backend {external_launcher,mp,ray,uni,None}]
                     [--pipeline-parallel-size PIPELINE_PARALLEL_SIZE]
                     [--tensor-parallel-size TENSOR_PARALLEL_SIZE]
                     [--data-parallel-size DATA_PARALLEL_SIZE]
                     [--enable-expert-parallel | --no-enable-expert-parallel]
                     [--max-parallel-loading-workers MAX_PARALLEL_LOADING_WORKERS]
                     [--ray-workers-use-nsight | --no-ray-workers-use-nsight]
                     [--disable-custom-all-reduce | --no-disable-custom-all-reduce]
                     [--block-size {1,8,16,32,64,128}]
                     [--gpu-memory-utilization GPU_MEMORY_UTILIZATION]
                     [--swap-space SWAP_SPACE]
                     [--kv-cache-dtype {auto,fp8,fp8_e4m3,fp8_e5m2}]
                     [--num-gpu-blocks-override NUM_GPU_BLOCKS_OVERRIDE]
                     [--enable-prefix-caching | --no-enable-prefix-caching]
                     [--prefix-caching-hash-algo {builtin,sha256}]
                     [--cpu-offload-gb CPU_OFFLOAD_GB]
                     [--calculate-kv-scales | --no-calculate-kv-scales]
                     [--disable-sliding-window] [--use-v2-block-manager]
                     [--seed SEED] [--max-logprobs MAX_LOGPROBS]
                     [--disable-log-stats]
                     [--quantization {aqlm,awq,deepspeedfp,tpu_int8,fp8,ptpc_fp8,fbgemm_fp8,modelopt,nvfp4,marlin,bitblas,gguf,gptq_marlin_24,gptq_marlin,gptq_bitblas,awq_marlin,gptq,compressed-tensors,bitsandbytes,qqq,hqq,experts_int8,neuron_quant,ipex,quark,moe_wna16,torchao,None}]
                     [--rope-scaling ROPE_SCALING] [--rope-theta ROPE_THETA]
                     [--hf-token [HF_TOKEN]] [--hf-overrides HF_OVERRIDES]
                     [--enforce-eager]
                     [--max-seq-len-to-capture MAX_SEQ_LEN_TO_CAPTURE]
                     [--tokenizer-pool-size TOKENIZER_POOL_SIZE]
                     [--tokenizer-pool-type TOKENIZER_POOL_TYPE]
                     [--tokenizer-pool-extra-config TOKENIZER_POOL_EXTRA_CONFIG]
                     [--limit-mm-per-prompt LIMIT_MM_PER_PROMPT]
                     [--mm-processor-kwargs MM_PROCESSOR_KWARGS]
                     [--disable-mm-preprocessor-cache]
                     [--enable-lora | --no-enable-lora]
                     [--enable-lora-bias | --no-enable-lora-bias]
                     [--max-loras MAX_LORAS] [--max-lora-rank MAX_LORA_RANK]
                     [--lora-extra-vocab-size LORA_EXTRA_VOCAB_SIZE]
                     [--lora-dtype {auto,bfloat16,float16}]
                     [--long-lora-scaling-factors LONG_LORA_SCALING_FACTORS [LONG_LORA_SCALING_FACTORS ...]]
                     [--max-cpu-loras MAX_CPU_LORAS]
                     [--fully-sharded-loras | --no-fully-sharded-loras]
                     [--enable-prompt-adapter | --no-enable-prompt-adapter]
                     [--max-prompt-adapters MAX_PROMPT_ADAPTERS]
                     [--max-prompt-adapter-token MAX_PROMPT_ADAPTER_TOKEN]
                     [--device {auto,cpu,cuda,hpu,neuron,tpu,xpu}]
                     [--speculative-config SPECULATIVE_CONFIG]
                     [--ignore-patterns IGNORE_PATTERNS]
                     [--served-model-name SERVED_MODEL_NAME [SERVED_MODEL_NAME ...]]
                     [--qlora-adapter-name-or-path QLORA_ADAPTER_NAME_OR_PATH]
                     [--show-hidden-metrics-for-version SHOW_HIDDEN_METRICS_FOR_VERSION]
                     [--otlp-traces-endpoint OTLP_TRACES_ENDPOINT]
                     [--collect-detailed-traces COLLECT_DETAILED_TRACES]
                     [--disable-async-output-proc]
                     [--max-num-batched-tokens MAX_NUM_BATCHED_TOKENS]
                     [--max-num-seqs MAX_NUM_SEQS]
                     [--max-num-partial-prefills MAX_NUM_PARTIAL_PREFILLS]
                     [--max-long-partial-prefills MAX_LONG_PARTIAL_PREFILLS]
                     [--long-prefill-token-threshold LONG_PREFILL_TOKEN_THRESHOLD]
                     [--num-lookahead-slots NUM_LOOKAHEAD_SLOTS]
                     [--scheduler-delay-factor SCHEDULER_DELAY_FACTOR]
                     [--preemption-mode {recompute,swap,None}]
                     [--num-scheduler-steps NUM_SCHEDULER_STEPS]
                     [--multi-step-stream-outputs | --no-multi-step-stream-outputs]
                     [--scheduling-policy {fcfs,priority}]
                     [--enable-chunked-prefill | --no-enable-chunked-prefill]
                     [--disable-chunked-mm-input | --no-disable-chunked-mm-input]
                     [--scheduler-cls SCHEDULER_CLS]
                     [--override-neuron-config OVERRIDE_NEURON_CONFIG]
                     [--override-pooler-config OVERRIDE_POOLER_CONFIG]
                     [--compilation-config COMPILATION_CONFIG]
                     [--kv-transfer-config KV_TRANSFER_CONFIG]
                     [--worker-cls WORKER_CLS]
                     [--worker-extension-cls WORKER_EXTENSION_CLS]
                     [--generation-config GENERATION_CONFIG]
                     [--override-generation-config OVERRIDE_GENERATION_CONFIG]
                     [--enable-sleep-mode]
                     [--additional-config ADDITIONAL_CONFIG]
                     [--enable-reasoning] [--disable-cascade-attn]
                     [--disable-log-requests] [--max-log-len MAX_LOG_LEN]
                     [--disable-fastapi-docs] [--enable-prompt-tokens-details]
                     [--enable-server-load-tracking]
api_server.py: error: unrecognized arguments: --disable-flash-attn
