import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor.transformers import oneshot
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier
import os

def custom_calibration_function(model, dataloader):
    """
    自定义校准函数，避免FX tracing问题
    """
    print("使用自定义校准函数，避免FX tracing...")
    
    model.eval()
    with torch.no_grad():
        for batch in dataloader:
            if isinstance(batch, dict):
                # 处理字典格式的batch
                if 'input_ids' in batch:
                    inputs = batch['input_ids']
                elif 'inputs' in batch:
                    inputs = batch['inputs']
                else:
                    # 取第一个tensor值
                    inputs = next(iter(batch.values()))
            else:
                inputs = batch
            
            # 确保inputs是tensor
            if not isinstance(inputs, torch.Tensor):
                continue
                
            # 移动到正确的设备
            if hasattr(model, 'device'):
                inputs = inputs.to(model.device)
            
            try:
                # 简单的forward pass，避免复杂的控制流
                _ = model(inputs)
            except Exception as e:
                print(f"校准过程中跳过一个batch: {e}")
                continue

def method1_custom_calibration():
    """
    方法1: 使用自定义校准函数
    """
    print("=== 方法1: 使用自定义校准函数避免FX tracing ===")
    
    # 模型路径和设备配置
    MODEL_ID = "/home/<USER>/single_llama"
    model = AutoModelForCausalLM.from_pretrained(
        MODEL_ID, device_map="auto", torch_dtype="auto",
    )
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    # 检查并添加填充标记
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    # 数据集加载和预处理
    NUM_CALIBRATION_SAMPLES = 256
    MAX_SEQUENCE_LENGTH = 1024
    
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(NUM_CALIBRATION_SAMPLES))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    ds = ds.map(preprocess)
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=MAX_SEQUENCE_LENGTH, truncation=True, add_special_tokens=False)
    
    ds = ds.map(tokenize, remove_columns=ds.column_names)
    
    try:
        # 使用自定义校准函数的SmoothQuant配置
        recipe = [
            SmoothQuantModifier(
                smoothing_strength=0.8,
                calibration_function=custom_calibration_function  # 关键：使用自定义校准函数
            ),
            GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
        ]
        
        print("开始SmoothQuant + GPTQ量化...")
        oneshot(
            model=model,
            dataset=ds,
            recipe=recipe,
            max_seq_length=MAX_SEQUENCE_LENGTH,
            num_calibration_samples=NUM_CALIBRATION_SAMPLES,
        )
        
        # 保存压缩后的模型
        SAVE_DIR = f"{MODEL_ID.split('/')[-1]}-W8A8-SmoothQuant-Custom"
        model.save_pretrained(SAVE_DIR, save_compressed=True)
        tokenizer.save_pretrained(SAVE_DIR)
        
        print(f"✅ 方法1成功！模型已保存到: {SAVE_DIR}")
        return True
        
    except Exception as e:
        print(f"❌ 方法1失败: {e}")
        return False

def method2_environment_variables():
    """
    方法2: 设置环境变量禁用FX tracing
    """
    print("\n=== 方法2: 设置环境变量禁用FX tracing ===")
    
    # 设置环境变量禁用FX相关功能
    os.environ['TORCH_FX_DISABLE'] = '1'
    os.environ['PYTORCH_JIT_USE_NNC_NOT_NVFUSER'] = '1'
    os.environ['TORCH_COMPILE_DISABLE'] = '1'
    
    # 模型路径和设备配置
    MODEL_ID = "/home/<USER>/single_llama"
    model = AutoModelForCausalLM.from_pretrained(
        MODEL_ID, device_map="auto", torch_dtype="auto",
    )
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    # 检查并添加填充标记
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    # 数据集加载和预处理
    NUM_CALIBRATION_SAMPLES = 256
    MAX_SEQUENCE_LENGTH = 1024
    
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(NUM_CALIBRATION_SAMPLES))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    ds = ds.map(preprocess)
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=MAX_SEQUENCE_LENGTH, truncation=True, add_special_tokens=False)
    
    ds = ds.map(tokenize, remove_columns=ds.column_names)
    
    try:
        # 标准SmoothQuant配置
        recipe = [
            SmoothQuantModifier(smoothing_strength=0.8),
            GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
        ]
        
        print("开始SmoothQuant + GPTQ量化（环境变量方法）...")
        oneshot(
            model=model,
            dataset=ds,
            recipe=recipe,
            max_seq_length=MAX_SEQUENCE_LENGTH,
            num_calibration_samples=NUM_CALIBRATION_SAMPLES,
        )
        
        # 保存压缩后的模型
        SAVE_DIR = f"{MODEL_ID.split('/')[-1]}-W8A8-SmoothQuant-EnvVar"
        model.save_pretrained(SAVE_DIR, save_compressed=True)
        tokenizer.save_pretrained(SAVE_DIR)
        
        print(f"✅ 方法2成功！模型已保存到: {SAVE_DIR}")
        return True
        
    except Exception as e:
        print(f"❌ 方法2失败: {e}")
        return False

def method3_explicit_mappings():
    """
    方法3: 使用显式mappings避免自动推断
    """
    print("\n=== 方法3: 使用显式mappings避免自动推断 ===")
    
    # 模型路径和设备配置
    MODEL_ID = "/home/<USER>/single_llama"
    model = AutoModelForCausalLM.from_pretrained(
        MODEL_ID, device_map="auto", torch_dtype="auto",
    )
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    # 检查并添加填充标记
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    # 数据集加载和预处理
    NUM_CALIBRATION_SAMPLES = 256
    MAX_SEQUENCE_LENGTH = 1024
    
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(NUM_CALIBRATION_SAMPLES))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    ds = ds.map(preprocess)
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=MAX_SEQUENCE_LENGTH, truncation=True, add_special_tokens=False)
    
    ds = ds.map(tokenize, remove_columns=ds.column_names)
    
    try:
        # 显式指定mappings，避免自动推断时的FX tracing
        explicit_mappings = [
            [["re:.*q_proj", "re:.*k_proj", "re:.*v_proj"], "re:.*input_layernorm"],
            [["re:.*gate_proj", "re:.*up_proj"], "re:.*post_attention_layernorm"]
        ]
        
        recipe = [
            SmoothQuantModifier(
                smoothing_strength=0.8,
                mappings=explicit_mappings,  # 显式指定mappings
                num_calibration_steps=128    # 限制校准步数
            ),
            GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
        ]
        
        print("开始SmoothQuant + GPTQ量化（显式mappings）...")
        oneshot(
            model=model,
            dataset=ds,
            recipe=recipe,
            max_seq_length=MAX_SEQUENCE_LENGTH,
            num_calibration_samples=NUM_CALIBRATION_SAMPLES,
        )
        
        # 保存压缩后的模型
        SAVE_DIR = f"{MODEL_ID.split('/')[-1]}-W8A8-SmoothQuant-Explicit"
        model.save_pretrained(SAVE_DIR, save_compressed=True)
        tokenizer.save_pretrained(SAVE_DIR)
        
        print(f"✅ 方法3成功！模型已保存到: {SAVE_DIR}")
        return True
        
    except Exception as e:
        print(f"❌ 方法3失败: {e}")
        return False

def main():
    """
    尝试多种方法解决SmoothQuant FX tracing问题
    """
    print("SmoothQuant FX Tracing 错误解决方案")
    print("=" * 60)
    
    methods = [
        method1_custom_calibration,
        method2_environment_variables, 
        method3_explicit_mappings,
    ]
    
    for i, method in enumerate(methods, 1):
        try:
            if method():
                print(f"\n🎉 方法{i}成功解决了FX tracing问题！")
                print("SmoothQuant量化已完成。")
                return True
        except Exception as e:
            print(f"方法{i}执行出错: {e}")
            continue
    
    print("\n❌ 所有方法都失败了")
    print("建议尝试以下替代方案：")
    print("1. 使用更新版本的llmcompressor")
    print("2. 使用不同的transformer版本")
    print("3. 考虑使用其他SmoothQuant实现")
    
    return False

if __name__ == "__main__":
    main()
